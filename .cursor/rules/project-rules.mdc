---
description: Zurno API Development Rules - AdonisJS E-commerce & Social Platform
globs: ['**/*.ts', '**/*.js', '**/*.json', '**/*.md']
alwaysApply: true
---

# Zurno API Development Rules - AdonisJS Project

## Project Overview

Zurno API is a comprehensive AdonisJS-based platform combining e-commerce, social media, affiliate marketing, AI assistance, and appointment booking features. Built with TypeScript, MySQL, Redis, and extensive third-party integrations.

## Core Technology Stack

### Backend Framework

- **AdonisJS v6** - Node.js MVC framework
- **TypeScript** - Primary language
- **Node.js >= 20.x** - Runtime environment

### Database & Storage

- **MySQL** - Primary database with read replicas
- **Redis** - Caching and session storage
- **Lucid ORM** - Database abstraction layer
- **AWS S3** - File storage via Flydrive

### Authentication & Authorization

- **JWT Authentication** - Custom JWT guard implementation
- **RBAC (Role-Based Access Control)** - Database-driven permissions
- **Bouncer** - Authorization policies

### Key Integrations

- **Shopify** - E-commerce platform integration
- **OpenAI** - AI assistance and chatbot
- **Twilio** - SMS and voice services
- **Firebase** - Push notifications
- **Pinecone** - Vector database for AI features
- **Bull Queue** - Job processing with Redis

## Project Structure & Organization

### Directory Structure

```
zurno-api/
├── app/                    # Application core
│   ├── controllers/        # HTTP controllers
│   ├── models/            # Lucid models
│   ├── services/          # Business logic services
│   ├── validators/        # VineJS validators
│   ├── middleware/        # HTTP middleware
│   ├── jobs/             # Queue jobs
│   ├── listeners/        # Event listeners
│   ├── mails/            # Email templates
│   └── exceptions/       # Exception handlers
├── admin/                 # Admin panel specific code
│   ├── controllers/       # Admin controllers
│   ├── validators/        # Admin validators
│   ├── services/         # Admin services
│   └── route/            # Admin routes
├── database/             # Database related
│   ├── migrations/       # Database migrations
│   ├── permissions/      # RBAC permission seeds
│   └── seeders/         # Database seeders
├── start/               # Application bootstrap
│   └── route/           # Route definitions
├── config/              # Configuration files
├── tests/               # Test suites
└── commands/            # Ace commands
```

## Naming Conventions

### Database & Models

- **Tables**: `zn_` prefix with snake_case (e.g., `zn_users`, `zn_products`)
- **Models**: PascalCase with `Zn` prefix (e.g., `ZnUser`, `ZnProduct`)
- **Columns**: camelCase in models, snake_case in database
- **Foreign Keys**: `{table}Id` format (e.g., `userId`, `productId`)
- **UUIDs**: Primary keys use UUID format

### Files & Classes

- **Controllers**: PascalCase with `Controller` suffix
- **Services**: PascalCase with `Service` suffix
- **Validators**: camelCase with `Validator` suffix
- **Jobs**: PascalCase with `Job` suffix
- **Middleware**: snake_case with `_middleware` suffix

### API Endpoints

- **Versioning**: `/v1/`, `/v2/` prefixes
- **Admin Routes**: `/v1/admin/` prefix
- **App Routes**: `/v1/app/` prefix
- **Resource Routes**: RESTful conventions (GET, POST, PUT, DELETE)

## Development Patterns

### Controller Structure

```typescript
export default class ExampleController {
  private exampleService: ExampleService

  constructor() {
    this.exampleService = new ExampleService()
  }

  async index({ request, response }: HttpContext) {
    // Implementation
  }
}
```

### Service Layer Pattern

- Business logic resides in service classes
- Controllers delegate to services
- Services handle data transformation and business rules
- Database operations through Lucid models

### Validation Pattern

```typescript
export const createExampleValidator = vine.compile(
  vine.object({
    field: vine.string().minLength(1),
    optionalField: vine.string().optional(),
  })
)
```

### Error Handling

- Custom exception handler in `app/exceptions/handler.ts`
- Validation errors return 422 with structured format
- Authorization errors return 403/401
- Consistent error response format

### Authentication Guards

- `jwt_admin` - Admin authentication
- `jwt_user` - User authentication
- Optional authentication pattern for engagement features

## Database Patterns

### Migration Conventions

- Timestamp prefix: `YYYYMMDDHHMMSS_description.ts`
- Use `node ace make:migration` command
- Always include `up()` and `down()` methods
- Foreign key constraints with proper cascading

### Model Relationships

```typescript
@hasMany(() => RelatedModel, {
  foreignKey: 'parentId',
  onQuery(query) {
    query.whereNull('deletedAt')
  }
})
declare items: HasMany<typeof RelatedModel>
```

### Soft Deletes

- Use `deletedAt` timestamp column
- Filter in relationship queries
- Maintain data integrity

## API Response Standards

### Success Responses

```typescript
// 200 OK
return response.ok(data)

// 201 Created
return response.created(data)
```

### Error Responses

```typescript
// 400 Bad Request
return response.badRequest({ message: 'Error description' })

// 422 Validation Error
return response.status(422).send({
  message: 'Validation failed',
  errors: validationErrors,
})
```

## Testing Standards

### Test Structure

- **Unit Tests**: `tests/unit/**/*.spec.ts`
- **Functional Tests**: `tests/functional/**/*.spec.ts`
- **Japa Framework**: Test runner with plugins
- **Database Transactions**: Rollback after each test

### Test Patterns

```typescript
test.group('Feature Name', (group) => {
  group.each.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  test('should perform action', async ({ assert, client }) => {
    // Test implementation
  })
})
```

## Queue & Job Processing

### Job Structure

```typescript
export default class ExampleJob extends BaseJob {
  async handle(payload: JobPayload) {
    // Job implementation
  }
}
```

### Queue Configuration

- Multiple queues: default, webhook, syncData, notification
- Redis-backed Bull Queue
- Retry mechanisms and error handling

## Security & Authorization

### RBAC Implementation

- Permission-based access control
- Database-stored permissions
- Bouncer integration for policy enforcement
- Resource-action permission format: `action:resource`

### Middleware Stack

- CORS handling
- Body parsing
- Authentication initialization
- User context binding
- Language localization

## Performance Considerations

### Caching Strategy

- Redis for session storage
- Response caching for expensive operations
- Database query optimization with indexes

### Database Optimization

- Read replicas for scaling
- Proper indexing on frequently queried columns
- Pagination for large datasets
- Eager loading for relationships

## Integration Patterns

### Third-Party Services

- Service classes for external API integration
- Error handling and retry logic
- Configuration through environment variables
- Webhook handling for real-time updates

### AI & ML Features

- OpenAI integration for chatbot and assistance
- Vector embeddings with Pinecone
- Natural language processing capabilities
- AI-powered recommendations

## Development Workflow

### Code Quality

- ESLint configuration with AdonisJS rules
- Prettier for code formatting
- TypeScript strict mode
- Husky for pre-commit hooks

### Environment Management

- `.env` files for configuration
- Separate configs for development/production
- Environment validation on startup

### Deployment

- PM2 ecosystem configuration
- Cluster mode for production
- Health checks and monitoring
- Database migration automation

## Critical Rules

### NEVER

- Skip validation on user inputs
- Expose sensitive data in API responses
- Ignore error handling in async operations
- Bypass authentication/authorization checks
- Commit sensitive data or credentials

### ALWAYS

- Use transactions for multi-table operations
- Validate and sanitize user inputs
- Follow the established naming conventions
- Write tests for new functionality
- Document API endpoints with Swagger
- Use services for business logic
- Handle errors gracefully
- Follow the MVC pattern
- Use proper HTTP status codes
- Implement proper logging

### Database Operations

- Use `node ace make:migration` for schema changes
- Follow camelCase for model properties
- Implement proper foreign key constraints
- Use soft deletes where appropriate
- Maintain backward compatibility

### API Development

- Follow RESTful conventions
- Version APIs appropriately
- Implement proper pagination
- Use consistent response formats
- Document with Swagger annotations

Remember: This is a complex e-commerce and social platform with multiple integrations. Maintain consistency with existing patterns and prioritize security, performance, and maintainability in all development decisions.
