import ZnAffiliate from '#models/zn_affiliate'
import ZnUser from '#models/zn_user'
import { AffiliateRefCodeService } from '#services/affiliation/affiliation_refcode_service'
import { AffiliationService } from '#services/affiliation/affiliation_service'
import {
  registerAffiliateValidator,
  updateAffiliateFieldValidator,
  videoReportQueryValidator,
} from '#validators/affiliate'
import { HttpContext } from '@adonisjs/core/http'
import { isValid, parse } from 'date-fns'

export default class AffiliateController {
  private affiliationService: AffiliationService
  private refCodeService: AffiliateRefCodeService

  constructor() {
    this.affiliationService = new AffiliationService()
    this.refCodeService = new AffiliateRefCodeService()
  }

  /**
   * @show
   * @tag Affiliation
   * @summary Get affiliate details
   * @description Get affiliate details. If provide startDate or endDate, the list of commisions and payments will be filtered by these date.
   * @paramQuery startDate - Start date - @type(DateTime)
   * @paramQuery endDate - End date - @type(DateTime)
   * @responseBody 201 - <ZnAffiliate>.append("id":"d6b54abc-756d-40fe-b9d6-d4c57f6efa49")
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async show({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      return await ZnAffiliate.query()
        .preload('affiliateTier')
        .preload('refCode')
        .preload('socialPages')
        .where('userId', user.id)
        .first()
    } catch (error) {
      console.log(error)
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Affiliation
   * @summary Get REF Code of affiliate
   * @description Get REF Code details of an affiliate
   * @paramQuery id - The ID of the REF Code - @type(string)
   * @responseBody 201 - <ZnAffiliateRefCode>.append("id":"d6b54abc-756d-40fe-b9d6-d4c57f6efa49")
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async getRefCode({ params, response }: HttpContext) {
    try {
      const refCodeId = params.id
      if (!refCodeId) {
        throw new Error('REF Code ID is required')
      }

      const refCode = await this.refCodeService.getRefCode(refCodeId)
      return response.ok(refCode)
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  async stats({ auth, request, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const affiliate = await ZnAffiliate.findByOrFail('userId', user.id)

      const startDateParam = request.input('startDate')
      const endDateParam = request.input('endDate')

      const startDate = this.parseDate(startDateParam)
      const endDate = this.parseDate(endDateParam)

      const stats = await this.affiliationService.getStatistics(affiliate.id, {
        from: startDate,
        to: endDate,
      })

      return response.ok(stats)
    } catch (error) {
      console.log(error)
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  async commissions({ auth, request, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const affiliate = await ZnAffiliate.findByOrFail('userId', user.id)

      const {
        startDate: startDateParam,
        endDate: endDateParam,
        page = 1,
        limit = 10,
      } = request.all()

      const startDate = this.parseDate(startDateParam)
      const endDate = this.parseDate(endDateParam)

      const commissions = await this.affiliationService.getCommissions(
        affiliate.id,
        { from: startDate, to: endDate },
        page,
        limit
      )

      return response.ok(commissions)
    } catch (error) {
      console.log(error)
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  async payouts({ auth, request, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const affiliate = await ZnAffiliate.findByOrFail('userId', user.id)

      const startDateParam = request.input('startDate')
      const endDateParam = request.input('endDate')

      const startDate = this.parseDate(startDateParam)
      const endDate = this.parseDate(endDateParam)

      const payouts = await this.affiliationService.getPayouts(affiliate.id, {
        from: startDate,
        to: endDate,
      })

      return response.ok(payouts)
    } catch (error) {
      console.log(error)
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @mostView
   * @tag Affiliation
   * @summary Get most viewed videos for the authenticated affiliate
   * @description Returns a list of videos (posts of type VIDEO) for the affiliate, with stats: upload date, name, thumbnail, URL, total view number, total products attached, total sold items, total commission. Supports filtering by startDate and endDate (on upload date).
   * @paramQuery startDate - Start date - @type(DateTime)
   * @paramQuery endDate - End date - @type(DateTime)
   * @responseBody 200 - List of video stats
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async mostView({ auth, request, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const startDateParam = request.input('startDate')
      const endDateParam = request.input('endDate')
      const startDate = this.parseDate(startDateParam)
      const endDate = this.parseDate(endDateParam)
      const data = await this.affiliationService.getMostViewedVideosForAffiliate(user.id, {
        from: startDate,
        to: endDate,
      })
      return response.ok(data)
    } catch (error) {
      console.log(error)
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  parseDate(inputDate: string): Date | null {
    if (!inputDate) return null

    const date = parse(inputDate, 'yyyy-MM-dd', new Date())
    return isValid(date) ? date : null
  }

  /**
   * @store
   * @tag Affiliation
   * @summary Register a user for affiliate
   * @requestBody { "socialPages": [ "https://facebook.com/example", "https://telegram.com/example" ]}
   * @responseBody 201 - { 'success': true, 'message': 'Registered successfully', 'data': <ZnAffiliate>}
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async store({ request, response, auth }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser
    const data = request.all()

    try {
      const payload = await registerAffiliateValidator.validate(data)

      const registrationData = {
        userId: user.id,
        socialPages: payload.socialPages,
      }

      const result = await this.affiliationService.registerAffiliate(registrationData)

      if (result.success) return response.created(result)
      else return response.notModified(result)
    } catch (error) {
      console.log(error)
      if (error.messages) {
        return response.badRequest({
          success: false,
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        success: false,
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @update
   * @tag Affiliation
   * @summary Update affiliate registration
   * @requestBody { "socialPages": [ "https://facebook.com/example", "https://telegram.com/example" ]}
   * @responseBody 201 - <ZnAffiliate>.append("id":"d6b54abc-756d-40fe-b9d6-d4c57f6efa49")
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async update({ request, response, auth }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const data = request.all()
      const payload = await registerAffiliateValidator.validate(data)

      const registrationData = {
        userId: user.id,
        socialPages: payload.socialPages,
      }

      const updatedAffiliate =
        await this.affiliationService.updateAffiliateRegistration(registrationData)

      return response.accepted(updatedAffiliate!.serialize())
    } catch (error) {
      console.log(error)
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @update
   * @tag Affiliation
   * @summary Update a field of affiliate
   * @requestBody { "fieldname": "value"}
   * @responseBody 201 - <ZnAffiliate>.append("id":"d6b54abc-756d-40fe-b9d6-d4c57f6efa49")
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async updateField({ request, response, auth }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const data = request.all()
      const payload = await updateAffiliateFieldValidator.validate(data)

      if (payload.shareLinkWithRefCode !== undefined) {
        const updatedAffiliate = await this.affiliationService.updateShareLinkWithRefCode(
          user.id,
          payload.shareLinkWithRefCode
        )
        return response.accepted(updatedAffiliate)
      } else {
        return response.notModified()
      }
    } catch (error) {
      console.log(error)
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @getVideoReport
   * @tag Video Report
   * @summary Get video report metrics
   * @description Get video report metrics including video count, total views, total products, and items sold. Supports date filtering similar to affiliate stats.
   * @paramQuery startDate - Start date in ISO 8601 format (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ) - @type(string) @optional
   * @paramQuery endDate - End date in ISO 8601 format (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ) - @type(string) @optional
   * @responseBody 200 - {"videoCount":24,"totalViews":10500,"totalProducts":150,"itemsSold":80} - Video report metrics
   * @responseBody 400 - {"message":"Validation failed","errors":[]} - Validation errors
   * @responseBody 401 - {"message":"Unauthorized access"} - Unauthorized access
   * @responseBody 500 - {"message":"Something went wrong"} - Internal server error
   */
  async getVideoReport({ auth, request, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const validatedQuery = await request.validateUsing(videoReportQueryValidator)

      if (
        validatedQuery.startDate &&
        validatedQuery.endDate &&
        validatedQuery.startDate >= validatedQuery.endDate
      ) {
        return response.badRequest({
          message: 'Validation failed',
          errors: [
            {
              field: 'startDate',
              message: 'startDate must be before endDate',
              rule: 'dateRange',
            },
          ],
        })
      }

      const report = await this.affiliationService.getVideoReport(user.id, {
        from: validatedQuery.startDate,
        to: validatedQuery.endDate,
      })

      return response.ok(report)
    } catch (error) {
      console.error('Video report error:', error)

      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      if (error.code === 'E_UNAUTHORIZED_ACCESS') {
        return response.unauthorized({
          message: 'Unauthorized access',
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }
}
