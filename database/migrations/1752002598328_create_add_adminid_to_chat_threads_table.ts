// database/migrations/XXXX_add_adminid_to_chat_threads.ts
import {BaseSchema} from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  public async up () {
    this.schema.alterTable('zn_chat_threads', (table) => {

      table.uuid('adminId').nullable()

      table
        .foreign('adminId')
        .references('id')
        .inTable('zn_admins')
    })
  }

  public async down () {
    this.schema.alterTable('zn_chat_threads', (table) => {
      table.dropForeign(['adminId'])
      table.dropColumn('adminId')
      table.uuid('userId').notNullable().alter()
    })
  }
}
