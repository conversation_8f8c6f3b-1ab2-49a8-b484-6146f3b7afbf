import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_chat_messages'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {

      table.uuid('annotatedChatMessageId').nullable()
      table
        .foreign('annotatedChatMessageId')
        .references('id')
        .inTable(this.tableName)
      .onDelete('CASCADE')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
