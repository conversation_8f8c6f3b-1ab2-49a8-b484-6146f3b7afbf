# Data Types

## Overview

This document defines all data types, validation rules, enum values, and data transformation patterns used throughout the Zurno API system. The API uses TypeScript with AdonisJS and Lucid ORM for type safety and data validation.

## Core Data Types

### 1. Primary Key Types

#### UUID Primary Keys

All database tables use UUID strings as primary keys for global uniqueness and scalability.

```typescript
// Database Column
@column({ isPrimary: true })
declare id: string

// Generated Format
// Example: "a1b2c3d4-e5f6-7890-1234-567890abcdef"
```

#### Shopify Integration IDs

External service identifiers maintain their original format.

```typescript
// Shopify Product ID
@column({ columnName: 'shopifyProductId' })
declare shopifyProductId: string
// Format: "gid://shopify/Product/123456789"

// Shopify Customer ID
@column({ columnName: 'shopifyCustomerId' })
declare shopifyCustomerId: string
// Format: "123456789"
```

### 2. Timestamp Types

#### Standard Timestamps

```typescript
// Auto-managed timestamps
@column.dateTime({ autoCreate: true })
declare createdAt: DateTime

@column.dateTime({ autoCreate: true, autoUpdate: true })
declare updatedAt: DateTime

// Soft delete timestamp
@column.dateTime({ columnName: 'deletedAt' })
declare deletedAt: DateTime | null
```

#### Business-specific Timestamps

```typescript
// User verification
@column.dateTime({ columnName: 'verifiedAt' })
declare verifiedAt: DateTime

// Login tracking
@column.dateTime({ columnName: 'lastLoginAt' })
declare lastLoginAt: DateTime | null

// Login code expiration
@column.dateTime({ columnName: 'loginCodeExpiredAt' })
declare loginCodeExpiredAt: DateTime | null
```

### 3. Decimal and Currency Types

#### Financial Values

```typescript
// Product pricing
@column({
  consume: (value: string) => parseFloat(value),
  prepare: (value: number) => value?.toString()
})
declare price: number

// Commission calculations
@column({
  columnName: 'commissionAmount',
  consume: (value: string) => parseFloat(value)
})
declare commissionAmount: number

// Gross Merchandise Value
@column({
  columnName: 'grossMerchandiseValue',
  consume: (value: string) => parseFloat(value)
})
declare grossMerchandiseValue: number
```

### 4. Boolean Types

#### Standard Booleans

```typescript
// User status
@column()
declare active: boolean

// Feature flags
@column({ columnName: 'isActive' })
declare isActive: boolean

// Verification status
@column()
declare verified: boolean
```

#### Converted Booleans

```typescript
// MySQL tinyint to boolean conversion
@column({
  columnName: 'isDefault',
  consume: (value: number) => value !== 0,
  prepare: (value: boolean) => value ? 1 : 0
})
declare isDefault: boolean
```

## Enum Types

### 1. Status Enums

#### Approval Status

```typescript
export enum EApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  UNDER_REVIEW = 'under_review',
  CANCELLED = 'cancelled'
}

// Usage in models
@column({ columnName: 'registerStatus' })
declare registerStatus: EApprovalStatus
```

#### Product Status

```typescript
export enum EProductStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DRAFT = 'draft'
}

@column()
declare status: EProductStatus
```

#### Event Status

```typescript
export enum EEventStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
}
```

### 2. Business Process Enums

#### Payment Types

```typescript
export enum EPaymentType {
  PAYPAL = 'paypal',
  ACH_TRANSFER = 'ach_transfer',
  CHECK = 'check',
  BANK_TRANSFER = 'bank_transfer',
  OTHER = 'other'
}

@column({ columnName: 'paymentType' })
declare paymentType: EPaymentType
```

#### Campaign Types

```typescript
export enum ECampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum ECampaignSendType {
  SMS = 'sms',
  EMAIL = 'email',
  PUSH = 'push',
  ALL = 'all',
}
```

#### AI Assistant Roles

```typescript
export enum EAIAssistantRole {
  SHOPPING_ASSISTANT = 'SHOPPING_ASSISTANT',
  POST_ASSISTANT = 'POST_ASSISTANT',
  ORDER_ASSISTANT = 'ORDER_ASSISTANT',
  CUSTOMER_SERVICE = 'CUSTOMER_SERVICE',
}
```

### 3. Classification Enums

#### Product Classification

```typescript
export enum EProductClassification {
  MATERIAL = 'material',
  PRODUCT = 'product'
}

@column({ columnName: 'classification' })
declare classification: EProductClassification | null
```

#### User Types

```typescript
export enum EUserType {
  CUSTOMER = 'customer',
  VENDOR = 'vendor',
  AFFILIATE = 'affiliate',
  ADMIN = 'admin',
}
```

## Validation Rules

### 1. VineJS Validators

#### String Validation

```typescript
// Email validation
email: vine.string().email().normalizeEmail()

// Required string with length constraints
title: vine.string().minLength(1).maxLength(255)

// Optional string
description: vine.string().optional()

// URL validation
url: vine.string().url().optional()
```

#### Number Validation

```typescript
// Positive numbers
price: vine.number().positive()

// Integer with range
quantity: vine.number().withoutDecimals().min(1).max(1000)

// Decimal with precision
commissionRate: vine.number().positive().max(1)

// Optional number
capacity: vine.number().positive().optional()
```

#### Boolean Validation

```typescript
// Required boolean
active: vine.boolean()

// Optional boolean with default
isPublic: vine.boolean().optional()
```

#### Enum Validation

```typescript
// Enum validation
status: vine.enum(EApprovalStatus)

// Optional enum
paymentType: vine.enum(EPaymentType).optional()
```

### 2. Custom Validation Rules

#### UUID Validation

```typescript
// UUID format validation
id: vine.string().uuid()

// Foreign key UUID
userId: vine.string().uuid()
```

#### Date Validation

```typescript
// ISO date string
startDate: vine.date({
  formats: ['YYYY-MM-DD', 'YYYY-MM-DD HH:mm:ss'],
})

// Future date validation
eventDate: vine.date().after('today')
```

#### File Validation

```typescript
// Image upload validation
image: vine.file({
  size: '5mb',
  extnames: ['jpg', 'jpeg', 'png', 'webp'],
})

// Document validation
document: vine.file({
  size: '10mb',
  extnames: ['pdf', 'doc', 'docx'],
})
```

## Data Transformation Patterns

### 1. Model Serialization

#### Computed Properties

```typescript
@computed()
get fullName() {
  return `${this.firstName} ${this.lastName}`
}

@computed()
get displayPrice() {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(this.price)
}
```

#### Serialization Hooks

```typescript
serializeExtras() {
  return {
    // Count relationships
    productsCount: this.$extras.products_count,

    // Computed URLs
    shareUrl: `${process.env.WEBSITE_URL}/product/${this.handle}`,

    // Formatted dates
    formattedCreatedAt: this.createdAt.toFormat('MMM dd, yyyy')
  }
}
```

### 2. Database Transformations

#### JSON Column Handling

```typescript
// JSON column with type safety
@column({
  prepare: (value: object) => JSON.stringify(value),
  consume: (value: string) => JSON.parse(value)
})
declare metadata: Record<string, any>

// Typed JSON columns
@column({
  columnName: 'socialLinks',
  prepare: (value: SocialLinks) => JSON.stringify(value),
  consume: (value: string) => JSON.parse(value) as SocialLinks
})
declare socialLinks: SocialLinks
```

#### Encrypted Fields

```typescript
// Sensitive data encryption
@column({ serializeAs: null })
declare password: string

// API keys (never serialized)
@column({ serializeAs: null })
declare apiKey: string | null
```

### 3. API Response Transformations

#### Pagination Response

```typescript
interface PaginationResponse<T> {
  data: T[]
  meta: {
    total: number
    perPage: number
    currentPage: number
    lastPage: number
    firstPage: number
    firstPageUrl: string
    lastPageUrl: string
    nextPageUrl: string | null
    previousPageUrl: string | null
  }
}
```

#### Success Response

```typescript
interface SuccessResponse<T = any> {
  success: true
  data: T
  message?: string
  meta?: Record<string, any>
}
```

#### Error Response

```typescript
interface ErrorResponse {
  success: false
  message: string
  errors?: ValidationError[]
  code?: string
  statusCode: number
}

interface ValidationError {
  field: string
  message: string
  rule: string
}
```

## Advanced Type Patterns

### 1. Union Types

#### Status Unions

```typescript
type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded'
```

#### Conditional Types

```typescript
type UserWithRole<T extends EUserType> = T extends EUserType.ADMIN
  ? ZnAdmin
  : T extends EUserType.AFFILIATE
    ? ZnAffiliate
    : ZnUser
```

### 2. Generic Types

#### Repository Pattern

```typescript
interface Repository<T> {
  find(id: string): Promise<T | null>
  findMany(filters: Partial<T>): Promise<T[]>
  create(data: Partial<T>): Promise<T>
  update(id: string, data: Partial<T>): Promise<T>
  delete(id: string): Promise<void>
}
```

#### Service Response Pattern

```typescript
interface ServiceResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  code?: string
}
```

### 3. Relationship Types

#### Model Relationships

```typescript
// One-to-Many
@hasMany(() => ZnProduct, {
  foreignKey: 'vendorId'
})
declare products: HasMany<typeof ZnProduct>

// Many-to-Many
@manyToMany(() => ZnCollection, {
  pivotTable: 'zn_product_collections',
  pivotForeignKey: 'productId',
  pivotRelatedForeignKey: 'collectionId'
})
declare collections: ManyToMany<typeof ZnCollection>

// Belongs To
@belongsTo(() => ZnUser, {
  foreignKey: 'userId'
})
declare user: BelongsTo<typeof ZnUser>
```

## Localization Types

### 1. Translation Enums

```typescript
export enum TRANSLATIONS_ENUM {
  EN = 'en',
  ES = 'es',
  VI = 'vi',
  ZH = 'zh'
}

@column()
declare locale: TRANSLATIONS_ENUM
```

### 2. Multilingual Content

```typescript
interface MultilingualContent {
  en: string
  es?: string
  vi?: string
  zh?: string
}

// Usage in models
@column({
  prepare: (value: MultilingualContent) => JSON.stringify(value),
  consume: (value: string) => JSON.parse(value) as MultilingualContent
})
declare title: MultilingualContent
```

## File and Media Types

### 1. Media Metadata

```typescript
interface MediaMetadata {
  width?: number
  height?: number
  size: number
  mimeType: string
  originalName: string
  extension: string
}

@column({
  prepare: (value: MediaMetadata) => JSON.stringify(value),
  consume: (value: string) => JSON.parse(value) as MediaMetadata
})
declare metadata: MediaMetadata
```

### 2. File Storage Paths

```typescript
// S3 storage path pattern
type S3Path = `s3://${string}/${string}`

// CDN URL pattern
type CDNUrl = `https://cdn.zurno.com/${string}`

@column({ columnName: 'filePath' })
declare filePath: S3Path

@column({ columnName: 'publicUrl' })
declare publicUrl: CDNUrl
```

This comprehensive data types documentation ensures type safety, validation consistency, and clear data contracts throughout the Zurno API system.
