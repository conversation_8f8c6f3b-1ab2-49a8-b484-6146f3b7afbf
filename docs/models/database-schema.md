# Database Schema

## Overview

The Zurno API database schema is designed to support a multi-domain B2B beauty supply platform. The schema uses MySQL with UUID primary keys, follows soft delete patterns, and implements comprehensive relationships between business entities.

## Schema Design Principles

### Naming Conventions

- **Table Prefix**: All tables use `zn_` prefix
- **Table Names**: Snake_case, plural (e.g., `zn_users`, `zn_products`)
- **Column Names**: Snake_case in database, mapped to camelCase in models
- **Primary Keys**: UUID strings for global uniqueness
- **Foreign Keys**: `{entity}_id` format (e.g., `user_id`, `product_id`)

### Common Patterns

- **Timestamps**: `created_at`, `updated_at` on all tables
- **Soft Deletes**: `deleted_at` timestamp for most entities
- **Audit Fields**: Integration with auditing package for change tracking
- **Status Fields**: Enum-based status tracking where applicable

## Core Business Entities

### User Management

#### zn_users

Primary customer accounts integrated with Shopify.

```sql
CREATE TABLE zn_users (
  id VARCHAR(255) PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255) NULL,
  first_name VARCHAR(255) NOT NULL,
  last_name VARCHAR(255) NOT NULL,
  avatar VARCHAR(255) NULL,
  avatar_id VARCHAR(255) NULL,
  friendly_url VARCHAR(255) NULL,
  social_provider VARCHAR(255) NULL,
  social_id VARCHAR(255) NULL,
  active BOOLEAN DEFAULT true,
  timezone VARCHAR(255) NULL,
  stripe_id VARCHAR(255) NULL,
  default_address_id VARCHAR(255) NULL,
  verified_at TIMESTAMP NULL,
  login_code VARCHAR(6) NULL,
  login_code_expired_at TIMESTAMP NULL,
  last_login_at TIMESTAMP NULL,
  device_token VARCHAR(255) NULL,
  birthday DATE NULL,
  shopify_customer_id VARCHAR(255) NULL,
  reward_points INT DEFAULT 0,
  gender VARCHAR(255) NULL,
  phone VARCHAR(255) NULL,
  latitude VARCHAR(255) NULL,
  longitude VARCHAR(255) NULL,
  smile_id INT NULL,
  no_discount INT DEFAULT 0,
  mile VARCHAR(255) NULL,
  cover_id VARCHAR(255) NULL,
  share_url VARCHAR(255) NULL,
  receive_post_notifications BOOLEAN DEFAULT true,
  vendor_id VARCHAR(255) NULL,
  locale VARCHAR(255) DEFAULT 'en',
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (avatar_id) REFERENCES zn_media(id),
  FOREIGN KEY (default_address_id) REFERENCES zn_addresses(id),
  FOREIGN KEY (cover_id) REFERENCES zn_media(id),
  FOREIGN KEY (vendor_id) REFERENCES zn_vendors(id),

  INDEX idx_email (email),
  INDEX idx_shopify_customer_id (shopify_customer_id),
  INDEX idx_active (active),
  INDEX idx_vendor_id (vendor_id)
);
```

**Key Features**:

- Integrated with Shopify customer accounts
- Social login support (Firebase)
- Email verification with temporary codes
- Reward points system
- Soft delete support

#### zn_admins

Administrative user accounts with role-based permissions.

```sql
CREATE TABLE zn_admins (
  id VARCHAR(255) PRIMARY KEY,
  username VARCHAR(255) NOT NULL UNIQUE,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  is_super_admin BOOLEAN DEFAULT false,
  last_login_at TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_is_active (is_active)
);
```

### E-commerce Entities

#### zn_products

Core product catalog with Shopify integration.

```sql
CREATE TABLE zn_products (
  id VARCHAR(255) PRIMARY KEY,
  shopify_product_id VARCHAR(255) NOT NULL,
  fulfil_product_id VARCHAR(255) NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT NULL,
  vendor_id VARCHAR(255) NULL,
  product_type_id VARCHAR(255) NULL,
  handle VARCHAR(255) NOT NULL,
  is_gift BOOLEAN DEFAULT false,
  price DECIMAL(10,2) NOT NULL,
  compare_at_price DECIMAL(10,2) NULL,
  status ENUM('active', 'archived', 'draft') DEFAULT 'draft',
  published_at TIMESTAMP NULL,
  category_id VARCHAR(255) NULL,
  pickup_only BOOLEAN DEFAULT false,
  classification ENUM('material', 'product') NULL,
  pending_changes JSON NULL,
  pending_approval ENUM('approve', 'reject', 'update', 'delete') NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (vendor_id) REFERENCES zn_vendors(id),
  FOREIGN KEY (product_type_id) REFERENCES zn_product_types(id),
  FOREIGN KEY (category_id) REFERENCES zn_product_categories(id),

  INDEX idx_shopify_product_id (shopify_product_id),
  INDEX idx_vendor_id (vendor_id),
  INDEX idx_status (status),
  INDEX idx_title (title),
  INDEX idx_category_id (category_id)
);
```

#### zn_product_variants

Product variants with pricing and inventory.

```sql
CREATE TABLE zn_product_variants (
  id VARCHAR(255) PRIMARY KEY,
  product_id VARCHAR(255) NOT NULL,
  shopify_variant_id VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  compare_at_price DECIMAL(10,2) NULL,
  sku VARCHAR(255) NULL,
  position INT DEFAULT 1,
  inventory_quantity INT DEFAULT 0,
  weight DECIMAL(8,2) NULL,
  weight_unit VARCHAR(255) DEFAULT 'lb',
  requires_shipping BOOLEAN DEFAULT true,
  taxable BOOLEAN DEFAULT true,
  image_id VARCHAR(255) NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (product_id) REFERENCES zn_products(id) ON DELETE CASCADE,
  FOREIGN KEY (image_id) REFERENCES zn_media(id),

  INDEX idx_product_id (product_id),
  INDEX idx_shopify_variant_id (shopify_variant_id),
  INDEX idx_sku (sku)
);
```

#### zn_orders

Order management with Shopify integration.

```sql
CREATE TABLE zn_orders (
  id VARCHAR(255) PRIMARY KEY,
  shopify_order_id VARCHAR(255) NOT NULL,
  order_number VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NULL,
  email VARCHAR(255) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  subtotal_price DECIMAL(10,2) NOT NULL,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  shipping_amount DECIMAL(10,2) DEFAULT 0,
  currency VARCHAR(3) DEFAULT 'USD',
  financial_status VARCHAR(255) NOT NULL,
  fulfillment_status VARCHAR(255) NULL,
  order_status_url VARCHAR(255) NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (user_id) REFERENCES zn_users(id),

  INDEX idx_shopify_order_id (shopify_order_id),
  INDEX idx_user_id (user_id),
  INDEX idx_order_number (order_number),
  INDEX idx_financial_status (financial_status)
);
```

### Store Management

#### zn_stores

Physical store locations with social features.

```sql
CREATE TABLE zn_stores (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  latitude VARCHAR(255) NOT NULL,
  longitude VARCHAR(255) NOT NULL,
  address TEXT NOT NULL,
  phone_number VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  website VARCHAR(255) NULL,
  viet_id INT NULL,
  is_zurno BOOLEAN DEFAULT false,
  working_hour JSON NULL,
  thumbnail_id VARCHAR(255) NULL,
  logo_id VARCHAR(255) NULL,
  cover_id VARCHAR(255) NULL,
  country_id VARCHAR(255) NOT NULL,
  state_id VARCHAR(255) NOT NULL,
  city_id VARCHAR(255) NOT NULL,
  zip_code VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  place_id VARCHAR(255) NULL,
  verified BOOLEAN DEFAULT false,
  timezone VARCHAR(255) NOT NULL,
  created_by_admin_id VARCHAR(255) NOT NULL,
  is_manage_booking_enabled BOOLEAN DEFAULT false,
  socials JSON NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (thumbnail_id) REFERENCES zn_media(id),
  FOREIGN KEY (logo_id) REFERENCES zn_media(id),
  FOREIGN KEY (cover_id) REFERENCES zn_media(id),
  FOREIGN KEY (country_id) REFERENCES zn_countries(id),
  FOREIGN KEY (state_id) REFERENCES zn_states(id),
  FOREIGN KEY (city_id) REFERENCES zn_cities(id),
  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  FOREIGN KEY (created_by_admin_id) REFERENCES zn_admins(id),

  INDEX idx_name (name),
  INDEX idx_user_id (user_id),
  INDEX idx_verified (verified),
  INDEX idx_city_id (city_id)
);
```

### Affiliate Marketing

#### zn_affiliates

Affiliate marketing accounts with tier-based commissions.

```sql
CREATE TABLE zn_affiliates (
  id VARCHAR(255) PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  tier_id VARCHAR(255) NOT NULL,
  register_date TIMESTAMP NOT NULL,
  register_status VARCHAR(255) DEFAULT 'pending',
  register_note TEXT NULL,
  item_sold INT DEFAULT 0,
  gross_merchandise_value DECIMAL(12,2) DEFAULT 0,
  ref_code_used INT DEFAULT 0,
  total_commissions DECIMAL(12,2) DEFAULT 0,
  total_payments DECIMAL(12,2) DEFAULT 0,
  ref_code_id VARCHAR(255) NOT NULL,
  share_link_with_ref_code BOOLEAN DEFAULT true,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  FOREIGN KEY (tier_id) REFERENCES zn_affiliate_tiers(id),
  FOREIGN KEY (ref_code_id) REFERENCES zn_affiliate_refcodes(id),

  INDEX idx_user_id (user_id),
  INDEX idx_tier_id (tier_id),
  INDEX idx_register_status (register_status)
);
```

#### zn_affiliate_commissions

Commission tracking and calculations.

```sql
CREATE TABLE zn_affiliate_commissions (
  id VARCHAR(255) PRIMARY KEY,
  affiliate_id VARCHAR(255) NOT NULL,
  order_id VARCHAR(255) NOT NULL,
  commission_amount DECIMAL(10,2) NOT NULL,
  commission_rate DECIMAL(5,2) NOT NULL,
  order_amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(255) DEFAULT 'pending',
  paid_at TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (affiliate_id) REFERENCES zn_affiliates(id),
  FOREIGN KEY (order_id) REFERENCES zn_orders(id),

  INDEX idx_affiliate_id (affiliate_id),
  INDEX idx_order_id (order_id),
  INDEX idx_status (status)
);
```

### Social Media

#### zn_posts

User-generated content with social features.

```sql
CREATE TABLE zn_posts (
  id VARCHAR(255) PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  store_id VARCHAR(255) NULL,
  category_id VARCHAR(255) NULL,
  title VARCHAR(255) NULL,
  content TEXT NOT NULL,
  media_urls JSON NULL,
  type ENUM('text', 'image', 'video', 'link') DEFAULT 'text',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published',
  visibility ENUM('public', 'private', 'followers') DEFAULT 'public',
  likes_count INT DEFAULT 0,
  comments_count INT DEFAULT 0,
  shares_count INT DEFAULT 0,
  scheduled_at TIMESTAMP NULL,
  published_at TIMESTAMP NULL,
  location VARCHAR(255) NULL,
  hashtags JSON NULL,
  mentioned_users JSON NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  FOREIGN KEY (store_id) REFERENCES zn_stores(id),
  FOREIGN KEY (category_id) REFERENCES zn_post_categories(id),

  INDEX idx_user_id (user_id),
  INDEX idx_store_id (store_id),
  INDEX idx_status (status),
  INDEX idx_published_at (published_at)
);
```

#### zn_post_comments

Comments on posts with threading support.

```sql
CREATE TABLE zn_post_comments (
  id VARCHAR(255) PRIMARY KEY,
  post_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  parent_id VARCHAR(255) NULL,
  content TEXT NOT NULL,
  likes_count INT DEFAULT 0,
  status ENUM('published', 'hidden', 'deleted') DEFAULT 'published',
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (post_id) REFERENCES zn_posts(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  FOREIGN KEY (parent_id) REFERENCES zn_post_comments(id),

  INDEX idx_post_id (post_id),
  INDEX idx_user_id (user_id),
  INDEX idx_parent_id (parent_id)
);
```

### Media Management

#### zn_media

Centralized media storage for images, videos, and files.

```sql
CREATE TABLE zn_media (
  id VARCHAR(255) PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  original_filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(255) NOT NULL,
  file_size INT NOT NULL,
  mime_type VARCHAR(255) NOT NULL,
  width INT NULL,
  height INT NULL,
  duration INT NULL,
  alt_text VARCHAR(255) NULL,
  description TEXT NULL,
  uploaded_by VARCHAR(255) NULL,
  s3_key VARCHAR(255) NULL,
  s3_bucket VARCHAR(255) NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (uploaded_by) REFERENCES zn_users(id),

  INDEX idx_filename (filename),
  INDEX idx_mime_type (mime_type),
  INDEX idx_uploaded_by (uploaded_by)
);
```

### AI and Assistance

#### zn_ai_assistants

AI assistant configurations and tracking.

```sql
CREATE TABLE zn_ai_assistants (
  id VARCHAR(255) PRIMARY KEY,
  role ENUM('shopping', 'post', 'order', 'customer_service') NOT NULL,
  openai_assistant_id VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  INDEX idx_role (role),
  INDEX idx_is_active (is_active)
);
```

### Notifications and Campaigns

#### notifications

System-wide notification management.

```sql
CREATE TABLE notifications (
  id VARCHAR(255) PRIMARY KEY,
  notifiable_id VARCHAR(255) NOT NULL,
  notifiable_type VARCHAR(255) NOT NULL,
  type VARCHAR(255) NOT NULL,
  data JSON NOT NULL,
  read_at TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  INDEX idx_notifiable (notifiable_id, notifiable_type),
  INDEX idx_type (type),
  INDEX idx_read_at (read_at)
);
```

#### zn_campaigns

Marketing campaign management.

```sql
CREATE TABLE zn_campaigns (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  type ENUM('email', 'sms', 'push', 'in_app') NOT NULL,
  status ENUM('draft', 'scheduled', 'running', 'completed', 'cancelled') DEFAULT 'draft',
  subject VARCHAR(255) NULL,
  content TEXT NOT NULL,
  template_id VARCHAR(255) NULL,
  audience_criteria JSON NULL,
  scheduled_at TIMESTAMP NULL,
  sent_at TIMESTAMP NULL,
  recipients_count INT DEFAULT 0,
  opened_count INT DEFAULT 0,
  clicked_count INT DEFAULT 0,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_scheduled_at (scheduled_at)
);
```

## Junction Tables

### Many-to-Many Relationships

#### zn_customers_stores

Customer-store relationships for tracking favorite stores.

```sql
CREATE TABLE zn_customers_stores (
  customer_id VARCHAR(255) NOT NULL,
  store_id VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL,

  PRIMARY KEY (customer_id, store_id),
  FOREIGN KEY (customer_id) REFERENCES zn_users(id) ON DELETE CASCADE,
  FOREIGN KEY (store_id) REFERENCES zn_stores(id) ON DELETE CASCADE
);
```

#### zn_users_like_posts

User post likes tracking.

```sql
CREATE TABLE zn_users_like_posts (
  user_id VARCHAR(255) NOT NULL,
  post_id VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL,

  PRIMARY KEY (user_id, post_id),
  FOREIGN KEY (user_id) REFERENCES zn_users(id) ON DELETE CASCADE,
  FOREIGN KEY (post_id) REFERENCES zn_posts(id) ON DELETE CASCADE
);
```

#### zn_product_collections

Product-collection relationships.

```sql
CREATE TABLE zn_product_collections (
  product_id VARCHAR(255) NOT NULL,
  collection_id VARCHAR(255) NOT NULL,
  position INT DEFAULT 1,
  created_at TIMESTAMP NOT NULL,

  PRIMARY KEY (product_id, collection_id),
  FOREIGN KEY (product_id) REFERENCES zn_products(id) ON DELETE CASCADE,
  FOREIGN KEY (collection_id) REFERENCES zn_collections(id) ON DELETE CASCADE
);
```

## Reference Data Tables

### Geographic Data

#### zn_countries

Country reference data.

```sql
CREATE TABLE zn_countries (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  code VARCHAR(2) NOT NULL UNIQUE,
  iso3 VARCHAR(3) NOT NULL UNIQUE,
  phone_code VARCHAR(10) NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);
```

#### zn_states

State/province reference data.

```sql
CREATE TABLE zn_states (
  id VARCHAR(255) PRIMARY KEY,
  country_id VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  code VARCHAR(10) NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (country_id) REFERENCES zn_countries(id),
  INDEX idx_country_id (country_id)
);
```

### Permission System

#### zn_permissions

RBAC permission definitions.

```sql
CREATE TABLE zn_permissions (
  id VARCHAR(255) PRIMARY KEY,
  action VARCHAR(255) NOT NULL,
  resource VARCHAR(255) NOT NULL,
  description TEXT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  UNIQUE KEY unique_permission (action, resource),
  INDEX idx_action (action),
  INDEX idx_resource (resource)
);
```

## Database Relationships Overview

### User-Centric Relationships

- Users → Posts (one-to-many)
- Users → Stores (one-to-many as owners)
- Users → Customers_Stores (many-to-many as customers)
- Users → Affiliates (one-to-one)
- Users → Orders (one-to-many)
- Users → Addresses (one-to-many)

### E-commerce Relationships

- Products → Product_Variants (one-to-many)
- Products → Product_Images (one-to-many)
- Products → Product_Collections (many-to-many)
- Orders → Order_Details (one-to-many)
- Vendors → Products (one-to-many)

### Affiliate Relationships

- Affiliates → Affiliate_Commissions (one-to-many)
- Affiliates → Affiliate_Tiers (many-to-one)
- Affiliate_Commissions → Orders (many-to-one)

### Social Relationships

- Posts → Post_Comments (one-to-many)
- Posts → Users_Like_Posts (many-to-many)
- Stores → Posts (one-to-many)

## Indexing Strategy

### Performance Indexes

- **Primary Keys**: Clustered indexes on all UUID primary keys
- **Foreign Keys**: Indexes on all foreign key columns
- **Search Fields**: Full-text indexes on title, description, content fields
- **Status Fields**: Indexes on status, active, published columns
- **Timestamp Fields**: Indexes on created_at, updated_at for sorting

### Composite Indexes

```sql
-- User activity tracking
INDEX idx_user_activity (user_id, created_at);

-- Product search optimization
INDEX idx_product_search (status, category_id, price);

-- Order management
INDEX idx_order_management (financial_status, fulfillment_status, created_at);

-- Affiliate performance
INDEX idx_affiliate_performance (affiliate_id, status, created_at);
```

## Data Integrity Constraints

### Referential Integrity

- Foreign key constraints maintain relationships
- Cascade deletes for dependent records
- Null constraints on required fields

### Business Rules

- Email uniqueness across users and admins
- Positive values for prices and quantities
- Valid status transitions through application logic
- Soft delete preservation of historical data

### Audit Trail

- All tables include created_at, updated_at timestamps
- Soft delete support with deleted_at timestamps
- Integration with auditing package for change tracking
- User attribution for all modifications

## Additional Business Entities

### AI & Machine Learning

#### zn_ai_assistants

AI assistant session management.

```sql
CREATE TABLE zn_ai_assistants (
  id VARCHAR(255) PRIMARY KEY,
  user_id VARCHAR(255) NULL,
  session_data JSON NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  INDEX idx_user_id (user_id)
);
```

### Campaign & Marketing

#### zn_campaigns

Marketing campaign management.

```sql
CREATE TABLE zn_campaigns (
  id VARCHAR(255) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT NULL,
  type ENUM('email', 'sms', 'push', 'banner') NOT NULL,
  status ENUM('draft', 'scheduled', 'active', 'completed', 'cancelled') DEFAULT 'draft',
  target_audience JSON NULL,
  content JSON NULL,
  scheduled_at TIMESTAMP NULL,
  started_at TIMESTAMP NULL,
  completed_at TIMESTAMP NULL,
  metrics JSON NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_scheduled_at (scheduled_at)
);
```

#### zn_email_logs

Email delivery tracking.

```sql
CREATE TABLE zn_email_logs (
  id VARCHAR(255) PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  status ENUM('pending', 'sent', 'failed', 'bounced') DEFAULT 'pending',
  sent_at TIMESTAMP NULL,
  campaign_id VARCHAR(255) NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (campaign_id) REFERENCES zn_campaigns(id),
  INDEX idx_email (email),
  INDEX idx_status (status),
  INDEX idx_campaign_id (campaign_id)
);
```

### Events & Activities

#### zn_events

Event management system.

```sql
CREATE TABLE zn_events (
  id VARCHAR(255) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT NULL,
  type ENUM('workshop', 'sale', 'launch', 'webinar') NOT NULL,
  status ENUM('draft', 'published', 'active', 'completed', 'cancelled') DEFAULT 'draft',
  max_participants INT NULL,
  registration_fee DECIMAL(10,2) DEFAULT 0.00,
  location VARCHAR(255) NULL,
  online_url VARCHAR(255) NULL,
  image_id VARCHAR(255) NULL,
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  registration_deadline TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (image_id) REFERENCES zn_media(id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_start_date (start_date)
);
```

#### zn_event_registrations

Event registration tracking.

```sql
CREATE TABLE zn_event_registrations (
  id VARCHAR(255) PRIMARY KEY,
  event_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  status ENUM('registered', 'attended', 'cancelled', 'no_show') DEFAULT 'registered',
  payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
  registration_data JSON NULL,
  registered_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (event_id) REFERENCES zn_events(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES zn_users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_registration (event_id, user_id),
  INDEX idx_event_id (event_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status)
);
```

### Live Streaming & Media

#### zn_streams

Live streaming management.

```sql
CREATE TABLE zn_streams (
  id VARCHAR(255) PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  store_id VARCHAR(255) NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT NULL,
  status ENUM('scheduled', 'live', 'ended', 'cancelled') DEFAULT 'scheduled',
  aws_ivs_channel_arn VARCHAR(255) NULL,
  aws_ivs_stream_key VARCHAR(255) NULL,
  aws_ivs_playback_url VARCHAR(255) NULL,
  thumbnail_id VARCHAR(255) NULL,
  viewer_count INT DEFAULT 0,
  max_viewers INT DEFAULT 0,
  scheduled_at TIMESTAMP NULL,
  started_at TIMESTAMP NULL,
  ended_at TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  FOREIGN KEY (store_id) REFERENCES zn_stores(id),
  FOREIGN KEY (thumbnail_id) REFERENCES zn_media(id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_scheduled_at (scheduled_at)
);
```

#### zn_stream_user_interactions

Stream viewer interactions.

```sql
CREATE TABLE zn_stream_user_interactions (
  id VARCHAR(255) PRIMARY KEY,
  stream_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NULL,
  interaction_type ENUM('join', 'leave', 'like', 'comment', 'share') NOT NULL,
  interaction_data JSON NULL,
  created_at TIMESTAMP NOT NULL,

  FOREIGN KEY (stream_id) REFERENCES zn_streams(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  INDEX idx_stream_id (stream_id),
  INDEX idx_user_id (user_id),
  INDEX idx_interaction_type (interaction_type)
);
```

### Tracking & Analytics

#### zn_tracking

User behavior and analytics tracking.

```sql
CREATE TABLE zn_tracking (
  id VARCHAR(255) PRIMARY KEY,
  user_id VARCHAR(255) NULL,
  session_id VARCHAR(255) NULL,
  event_type VARCHAR(255) NOT NULL,
  event_data JSON NULL,
  page_url VARCHAR(500) NULL,
  referrer VARCHAR(500) NULL,
  user_agent TEXT NULL,
  ip_address VARCHAR(45) NULL,
  country VARCHAR(255) NULL,
  city VARCHAR(255) NULL,
  device_type VARCHAR(255) NULL,
  created_at TIMESTAMP NOT NULL,

  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  INDEX idx_user_id (user_id),
  INDEX idx_session_id (session_id),
  INDEX idx_event_type (event_type),
  INDEX idx_created_at (created_at)
);
```

### Rewards & Gifts

#### zn_gifts

Gift management system.

```sql
CREATE TABLE zn_gifts (
  id VARCHAR(255) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT NULL,
  type ENUM('product', 'discount', 'points', 'service') NOT NULL,
  value DECIMAL(10,2) NULL,
  conditions JSON NULL,
  max_redemptions INT NULL,
  current_redemptions INT DEFAULT 0,
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  status ENUM('draft', 'active', 'expired', 'exhausted') DEFAULT 'draft',
  image_id VARCHAR(255) NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (image_id) REFERENCES zn_media(id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_start_date (start_date),
  INDEX idx_end_date (end_date)
);
```

#### zn_gift_registrations

Gift redemption tracking.

```sql
CREATE TABLE zn_gift_registrations (
  id VARCHAR(255) PRIMARY KEY,
  gift_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  status ENUM('registered', 'qualified', 'redeemed', 'expired') DEFAULT 'registered',
  qualification_data JSON NULL,
  redeemed_at TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  FOREIGN KEY (gift_id) REFERENCES zn_gifts(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES zn_users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_gift_user (gift_id, user_id),
  INDEX idx_gift_id (gift_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status)
);
```

### Communication

#### zn_chat_rooms

Chat room management.

```sql
CREATE TABLE zn_chat_rooms (
  id VARCHAR(255) PRIMARY KEY,
  type ENUM('direct', 'group', 'support', 'stream') NOT NULL,
  name VARCHAR(255) NULL,
  metadata JSON NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  INDEX idx_type (type)
);
```

#### zn_chat_messages

Chat message storage.

```sql
CREATE TABLE zn_chat_messages (
  id VARCHAR(255) PRIMARY KEY,
  room_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  message_type ENUM('text', 'image', 'file', 'system') DEFAULT 'text',
  content TEXT NOT NULL,
  metadata JSON NULL,
  edited_at TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (room_id) REFERENCES zn_chat_rooms(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES zn_users(id),
  INDEX idx_room_id (room_id),
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at)
);
```

### Language & Localization

#### zn_languages

Language support configuration.

```sql
CREATE TABLE zn_languages (
  id VARCHAR(255) PRIMARY KEY,
  code VARCHAR(5) NOT NULL UNIQUE,
  name VARCHAR(255) NOT NULL,
  native_name VARCHAR(255) NOT NULL,
  direction ENUM('ltr', 'rtl') DEFAULT 'ltr',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,

  INDEX idx_code (code),
  INDEX idx_is_active (is_active)
);
```

## Extended Junction Tables

### Additional Many-to-Many Relationships

#### zn_products_product_tags

Product tagging system.

```sql
CREATE TABLE zn_products_product_tags (
  product_id VARCHAR(255) NOT NULL,
  product_tag_id VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL,

  PRIMARY KEY (product_id, product_tag_id),
  FOREIGN KEY (product_id) REFERENCES zn_products(id) ON DELETE CASCADE,
  FOREIGN KEY (product_tag_id) REFERENCES zn_product_tags(id) ON DELETE CASCADE
);
```

#### zn_affiliates_payment_methods

Affiliate payment method preferences.

```sql
CREATE TABLE zn_affiliates_payment_methods (
  affiliate_id VARCHAR(255) NOT NULL,
  payment_method_id VARCHAR(255) NOT NULL,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP NOT NULL,

  PRIMARY KEY (affiliate_id, payment_method_id),
  FOREIGN KEY (affiliate_id) REFERENCES zn_affiliates(id) ON DELETE CASCADE,
  FOREIGN KEY (payment_method_id) REFERENCES zn_payment_methods(id) ON DELETE CASCADE
);
```

#### zn_products_channels

Product channel assignments.

```sql
CREATE TABLE zn_products_channels (
  product_id VARCHAR(255) NOT NULL,
  channel_id VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL,

  PRIMARY KEY (product_id, channel_id),
  FOREIGN KEY (product_id) REFERENCES zn_products(id) ON DELETE CASCADE,
  FOREIGN KEY (channel_id) REFERENCES zn_channels(id) ON DELETE CASCADE
);
```

## Database Views

### Performance Views

#### v_product_analytics

Product performance aggregation.

```sql
CREATE VIEW v_product_analytics AS
SELECT
  p.id,
  p.title,
  p.vendor_id,
  p.category_id,
  COUNT(DISTINCT od.id) as total_orders,
  SUM(od.quantity) as total_quantity_sold,
  AVG(pr.rating) as average_rating,
  COUNT(DISTINCT pr.id) as review_count,
  p.created_at
FROM zn_products p
LEFT JOIN zn_order_details od ON p.id = od.product_id
LEFT JOIN zn_product_reviews pr ON p.id = pr.product_id
WHERE p.deleted_at IS NULL
GROUP BY p.id;
```

#### v_affiliate_performance

Affiliate performance metrics.

```sql
CREATE VIEW v_affiliate_performance AS
SELECT
  a.id,
  a.user_id,
  a.tier_id,
  COUNT(DISTINCT ac.id) as total_commissions,
  SUM(ac.commission_amount) as total_earnings,
  COUNT(DISTINCT ac.order_id) as orders_generated,
  AVG(ac.commission_amount) as average_commission,
  a.created_at
FROM zn_affiliates a
LEFT JOIN zn_affiliate_commissions ac ON a.id = ac.affiliate_id
WHERE a.deleted_at IS NULL
GROUP BY a.id;
```

## Data Retention Policies

### Automatic Cleanup

#### Log Data Retention

```sql
-- Delete tracking data older than 2 years
DELETE FROM zn_tracking
WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- Archive old email logs
INSERT INTO zn_email_logs_archive
SELECT * FROM zn_email_logs
WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

#### Session Cleanup

```sql
-- Remove expired login codes
UPDATE zn_users
SET login_code = NULL, login_code_expired_at = NULL
WHERE login_code_expired_at < NOW();
```

### Data Archival Strategy

- **Transactional Data**: Permanent retention for orders, payments
- **User Activity**: 2-year retention for tracking and analytics
- **Communication Logs**: 1-year retention for emails and notifications
- **Session Data**: 30-day retention for authentication logs
- **Audit Trails**: 7-year retention for compliance

This comprehensive database schema supports the complex multi-domain requirements of the Zurno platform while maintaining data integrity, performance, and scalability across e-commerce, social media, affiliate marketing, AI assistance, and appointment booking functionalities.
