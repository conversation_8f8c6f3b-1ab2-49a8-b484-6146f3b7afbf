# Project Context

## Platform Overview

Zurno API is a comprehensive B2B beauty supply platform that serves as the technical backbone for a multi-faceted business ecosystem. The platform combines e-commerce, social media, affiliate marketing, AI assistance, and appointment booking into a unified system serving the beauty industry.

## Core Business Domains

### 1. E-commerce Platform

**Primary Focus**: B2B beauty supply sales with Shopify integration

**Key Features**:

- Product catalog with variants, options, and images
- Shopify-first architecture (all customers must exist in Shopify)
- Vendor/supplier management system
- Bundle products and pricing structures
- Product reviews and ratings
- Collection and category management
- Draft orders and order fulfillment
- Inventory tracking across multiple locations

**Business Model**: Multi-vendor marketplace where suppliers can list products and distributors/salons can purchase inventory.

### 2. Multi-Store Management System

**Purpose**: Physical store location discovery and management for beauty salons/shops

**Key Features**:

- Store profiles with location, contact info, working hours
- Store verification and claim functionality
- Customer-store relationships
- Store-specific social content (posts)
- Appointment booking system for salon services
- Integration with nail system categories
- Store owner/manager access controls

**Business Value**: Helps customers find local beauty services and enables salons to manage their online presence.

### 3. Affiliate Marketing Program

**Objective**: Drive sales through referral-based commission system

**Key Features**:

- Multi-tier affiliate structure with different commission rates
- Referral code generation and tracking
- Commission calculation based on GMV (Gross Merchandise Value)
- Affiliate payment processing and payout management
- Social page management for affiliate marketing
- Performance tracking (items sold, revenue generated)
- Integration with Shopify discount codes

**Revenue Model**: Affiliates earn commissions on successful referrals, driving customer acquisition.

### 4. Social Media Platform

**Goal**: Build community around beauty products and stores

**Key Features**:

- User-generated content (posts with images/videos)
- Social interactions (likes, comments, shares)
- Store-specific social feeds
- User profiles with social features
- Content moderation and engagement tracking

**Community Building**: Creates engagement around beauty topics and drives product discovery.

### 5. AI-Powered Assistant

**Innovation**: Multi-agent AI system for customer assistance

**Capabilities**:

- Shopping assistant for product recommendations
- Post content assistance and discovery
- Order support and customer service
- Customer service agent for general inquiries
- Vector database integration for semantic search
- Natural language product and content discovery

**Technology**: OpenAI GPT models with Pinecone vector database for contextual recommendations.

### 6. Live Streaming & Media

**Purpose**: Real-time product demonstrations and social commerce

**Features**:

- AWS IVS integration for live streaming
- Viewer tracking and engagement analytics
- Media management (images, videos)
- Stream interaction features
- Live commerce capabilities

## Business Relationships

### Customer Types

1. **End Customers**: Individual consumers purchasing beauty products
2. **Salon Owners**: Beauty professionals managing stores and booking appointments
3. **Distributors**: B2B customers purchasing inventory for resale
4. **Affiliates**: Marketing partners earning commissions on referrals
5. **Vendors/Suppliers**: Companies providing products to the marketplace

### Data Flow Between Domains

```
Shopify ↔ Zurno Users ↔ Store Management
    ↓           ↓              ↓
Orders → Affiliate Tracking → Commission Calculation
    ↓           ↓              ↓
AI Assistant ← Product Data ← Content Management
```

## Integration Ecosystem

### Core Integrations

**Shopify**: Primary e-commerce engine

- Customer management (required for all users)
- Product catalog synchronization
- Order processing and fulfillment
- Inventory management
- Discount code integration for affiliates

**Firebase**: Authentication and notifications

- Social login (Google, Facebook, etc.)
- Push notifications
- ID token verification

**AWS Services**: Infrastructure and media

- S3: File storage for images, videos, documents
- IVS: Live streaming platform
- SNS: Push notifications
- Polly: Text-to-speech for accessibility
- CloudWatch: Monitoring and logging

**OpenAI**: AI-powered features

- GPT models for chatbot agents
- Natural language processing
- Content analysis and recommendations

**Pinecone**: Vector database

- Semantic search for products and content
- AI recommendation engine
- Embedding storage for ML features

**Twilio**: Communication services

- SMS notifications
- Voice services for customer support

**Google Services**: Analytics and location

- BigQuery for data analytics
- Google Places for store location data

### Third-Party Services

- **1Viet**: Legacy system integration for existing store data
- **Smile.io**: Reward points management through Shopify
- **Fulfil**: Additional e-commerce and inventory management
- **Stamped**: Product review management

## User Journeys

### Customer Journey

1. **Discovery**: Find products through AI assistant, posts, or browsing
2. **Authentication**: Login via email verification or social login
3. **Shopping**: Add products to cart, apply affiliate discounts
4. **Purchase**: Complete checkout through Shopify integration
5. **Engagement**: Leave reviews, create posts, interact with community
6. **Support**: Get help through AI assistant or customer service

### Affiliate Journey

1. **Registration**: Apply for affiliate program
2. **Approval**: Admin reviews and approves application
3. **Marketing**: Share referral links and codes
4. **Tracking**: Monitor clicks, conversions, and commissions
5. **Payout**: Receive commission payments

### Store Owner Journey

1. **Claim Store**: Verify ownership of physical location
2. **Setup**: Configure store profile, working hours, services
3. **Content**: Create posts and manage social presence
4. **Bookings**: Enable appointment booking for services
5. **Analytics**: Track store performance and customer engagement

## Technical Architecture Principles

### API-First Design

- RESTful API architecture
- Versioned endpoints (`/v1/`, `/v2/`)
- Comprehensive Swagger documentation
- Consistent response formats

### Microservices Approach

- Service-oriented business logic
- Separation of concerns between domains
- Modular controller and service architecture

### Queue-Based Processing

- Background job processing for all heavy operations
- Multiple queue types for different workloads
- Reliable job processing with retry mechanisms

### Real-Time Features

- WebSocket integration for live features
- Push notifications for user engagement
- Live streaming capabilities

## Security & Compliance

### Data Protection

- JWT-based authentication
- Role-based access control (RBAC)
- Soft deletes to maintain data integrity
- Audit trails for all critical operations

### Business Compliance

- Integration with payment processors
- Tax calculation and management
- Inventory tracking and compliance
- Customer data protection (GDPR considerations)

## Scalability Considerations

### Database Strategy

- MySQL with read replicas for scaling
- Redis for caching and session management
- Proper indexing for performance

### Performance Optimization

- Response caching for expensive operations
- Background processing for heavy workloads
- CDN for media delivery

### Monitoring & Analytics

- CloudWatch for system monitoring
- BigQuery for business analytics
- Performance tracking across all domains

## Future Roadmap Considerations

### Planned Enhancements

- Advanced AI recommendations
- Enhanced live streaming features
- Mobile app development
- International expansion capabilities
- Advanced analytics and reporting

### Technical Debt Management

- Code refactoring for maintainability
- Documentation improvements
- Test coverage expansion
- Performance optimization

This project context serves as the foundation for understanding how all technical decisions, API designs, and business logic implementations support the overall business objectives of the Zurno platform.
