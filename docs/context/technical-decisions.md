# Technical Decisions

## Overview

This document outlines the key technical decisions made in the Zurno API architecture, their rationale, and the trade-offs considered. These decisions form the foundation of the platform's technical strategy.

## Core Technology Stack

### Backend Framework: AdonisJS v6

**Decision**: Use AdonisJS as the primary backend framework

**Rationale**:

- Full-featured MVC framework with TypeScript support
- Built-in ORM (Lucid) for database operations
- Comprehensive ecosystem (authentication, validation, mail, queues)
- Strong convention over configuration approach
- Active community and excellent documentation
- Built-in support for enterprise features (auditing, RBAC)

**Trade-offs**:

- Less ecosystem than Express.js
- Learning curve for team members unfamiliar with AdonisJS
- Framework lock-in vs. more flexible micro-framework approach

**Alternatives Considered**: Express.js, NestJS, Fastify

### Database: MySQL with Read Replicas

**Decision**: MySQL as primary database with read replica scaling

**Rationale**:

- ACID compliance for transactional e-commerce operations
- Mature ecosystem with excellent tooling
- Strong performance for structured data relationships
- Read replica support for scaling read operations
- Compatible with existing Shopify integrations
- Team expertise with SQL databases

**Trade-offs**:

- Requires more operational overhead than managed NoSQL
- Vertical scaling limitations compared to NoSQL solutions
- Schema migrations require careful planning

**Alternatives Considered**: PostgreSQL, MongoDB, MariaDB

### Cache & Sessions: Redis

**Decision**: Redis for caching and session storage

**Rationale**:

- High-performance in-memory data store
- Built-in support for complex data structures
- Pub/Sub capabilities for real-time features
- Session persistence for user authentication
- Queue backend compatibility with Bull Queue
- Atomic operations for counters and tracking

**Trade-offs**:

- Additional infrastructure component to manage
- Memory usage for large datasets
- Data persistence configuration complexity

**Alternatives Considered**: Memcached, In-memory caching, Database sessions

## Authentication Architecture

### Custom JWT Guards

**Decision**: Implement custom JWT authentication with separate guards for admin and users

**Rationale**:

- Stateless authentication suitable for API-first architecture
- Separate concerns between admin and user authentication
- Scalable across multiple services
- Mobile app compatibility
- Fine-grained control over token validation
- Integration with existing AdonisJS auth system

**Trade-offs**:

- More complex than session-based authentication
- Token refresh strategy required for long-lived sessions
- Custom implementation vs. standard OAuth2

**Alternatives Considered**: Session-based auth, OAuth2, Passport.js

### Shopify-First User Management

**Decision**: Require all users to exist in Shopify before Zurno authentication

**Rationale**:

- Unified customer database across platforms
- Leverages Shopify's customer management features
- Reward points integration through Shopify apps
- Single source of truth for customer data
- Simplified user data synchronization

**Trade-offs**:

- Dependency on Shopify for user creation
- Additional API calls for user verification
- Limited flexibility for non-e-commerce users

**Alternatives Considered**: Independent user management, Firebase as primary auth

## Queue System: Bull Queue with Redis

**Decision**: Use Bull Queue for background job processing

**Rationale**:

- Redis-backed for reliability and persistence
- Rich job scheduling and retry mechanisms
- Dashboard for monitoring and debugging
- Multiple queue support for different priorities
- Built-in support for delayed and recurring jobs
- Active maintenance and community support

**Trade-offs**:

- Redis dependency for queue operations
- Limited built-in job distribution across workers
- JavaScript/Node.js ecosystem lock-in

**Alternatives Considered**: AWS SQS, RabbitMQ, Agenda.js

## AI Architecture

### Multi-Agent System with OpenAI

**Decision**: Implement specialized AI agents for different business domains

**Rationale**:

- Domain-specific expertise for better responses
- Modular architecture allows independent agent improvement
- Specialized prompts and context for each use case
- Easier testing and debugging of individual agents
- Scalable architecture for adding new agent types

**Trade-offs**:

- More complex than single general-purpose agent
- Orchestration logic required for agent selection
- Increased API costs with multiple specialized calls

**Agent Types**:

- Shopping Assistant: Product recommendations and e-commerce queries
- Post Agent: Content discovery and social media assistance
- Order Agent: Order status and fulfillment support
- Customer Service: General inquiries and support

**Alternatives Considered**: Single general agent, Rule-based chatbot, Third-party chatbot service

### Vector Database: Pinecone

**Decision**: Use Pinecone for vector embeddings and semantic search

**Rationale**:

- Managed service reduces operational overhead
- High-performance vector similarity search
- Built-in support for metadata filtering
- Scalable architecture for growing data
- Integration with OpenAI embedding models
- Real-time updates for product catalog changes

**Trade-offs**:

- Vendor lock-in with managed service
- Cost considerations for large-scale embeddings
- Additional complexity in data pipeline

**Alternatives Considered**: Weaviate, Chroma, Self-hosted vector solutions

## Integration Strategy

### Shopify as E-commerce Engine

**Decision**: Use Shopify as the primary e-commerce platform with API integration

**Rationale**:

- Mature e-commerce platform with comprehensive features
- Strong ecosystem of apps and integrations
- Handles complex e-commerce logic (taxes, shipping, payments)
- Reduces development time for e-commerce features
- Proven scalability for high-volume commerce
- Built-in fraud protection and security

**Trade-offs**:

- API rate limits for heavy integration
- Platform dependency for core e-commerce functions
- Limited customization compared to custom e-commerce solution

**Integration Approach**:

- Webhook-based real-time synchronization
- GraphQL API for efficient data fetching
- REST API for simple operations
- Draft orders for complex checkout flows

### AWS Services for Infrastructure

**Decision**: Use AWS services for file storage, streaming, and communications

**Rationale**:

- Comprehensive service ecosystem
- Global CDN and edge locations
- Managed services reduce operational overhead
- Strong security and compliance features
- Cost-effective scaling options

**Services Used**:

- **S3**: File storage for images, videos, documents
- **IVS**: Live streaming with low latency
- **SNS**: Push notifications
- **CloudWatch**: Monitoring and logging
- **Polly**: Text-to-speech for accessibility

**Trade-offs**:

- Cloud vendor lock-in
- Complexity of managing multiple AWS services
- Cost optimization requires ongoing monitoring

## Data Architecture

### UUID Primary Keys

**Decision**: Use UUIDs instead of auto-incrementing integers for primary keys

**Rationale**:

- Globally unique identifiers across distributed systems
- Security through non-sequential IDs
- Easier data migration and replication
- Compatible with Shopify's ID system
- Prevents enumeration attacks

**Trade-offs**:

- Larger storage footprint than integers
- Slightly lower query performance
- Human readability concerns

### Soft Deletes

**Decision**: Implement soft deletes across critical business entities

**Rationale**:

- Data retention for audit and compliance
- Ability to restore accidentally deleted data
- Maintains referential integrity
- Historical reporting capabilities
- Customer data protection requirements

**Trade-offs**:

- Increased storage requirements
- Query complexity for filtering deleted records
- Index performance considerations

## API Design

### Versioned RESTful APIs

**Decision**: Implement versioned REST APIs with clear resource endpoints

**Rationale**:

- Clear API evolution path
- Backward compatibility for existing clients
- Resource-based design maps well to business entities
- Standard HTTP methods for CRUD operations
- Easy to document and understand

**Versioning Strategy**:

- URL path versioning (`/v1/`, `/v2/`)
- Major version increments for breaking changes
- Minor updates within existing versions

**Trade-offs**:

- Multiple API versions to maintain
- Migration complexity for client applications
- Documentation overhead

### Admin vs App API Separation

**Decision**: Separate API endpoints for admin and app functionality

**Rationale**:

- Clear separation of concerns
- Different authentication requirements
- Optimized responses for different use cases
- Independent evolution of admin and user features
- Security through access control

**Structure**:

- `/v1/admin/*` - Administrative functions
- `/v1/app/*` - User-facing features
- `/v1/*` - Public endpoints (products, posts)

## Development Practices

### TypeScript Throughout

**Decision**: Use TypeScript for all application code

**Rationale**:

- Type safety reduces runtime errors
- Better IDE support and developer experience
- Easier refactoring and maintenance
- Clear interfaces for API contracts
- Team productivity improvements

**Trade-offs**:

- Learning curve for JavaScript developers
- Compilation step in development process
- Type definition maintenance overhead

### Service Layer Pattern

**Decision**: Implement service layer for business logic separation

**Rationale**:

- Separation of concerns from controllers
- Reusable business logic across endpoints
- Easier testing of business rules
- Clear architectural boundaries
- Maintainable codebase structure

**Implementation**:

- Controllers handle HTTP concerns
- Services contain business logic
- Models represent data layer
- Validators handle input validation

## Performance Optimizations

### Response Caching

**Decision**: Implement Redis-based response caching for expensive operations

**Rationale**:

- Reduces database load for frequently accessed data
- Improves API response times
- Scales read operations efficiently
- Configurable cache TTL per operation type

**Caching Strategy**:

- Product catalog caching
- User session caching
- Search result caching
- API response caching for public endpoints

### Background Job Processing

**Decision**: Move all heavy operations to background jobs

**Rationale**:

- Improves API response times
- Reliable processing with retry mechanisms
- Scalable worker architecture
- User experience optimization

**Job Categories**:

- Data synchronization (Shopify, 1Viet)
- Notification delivery
- AI processing (embeddings, recommendations)
- Image processing and optimization
- Report generation

## Security Decisions

### Role-Based Access Control (RBAC)

**Decision**: Implement database-driven RBAC with Bouncer

**Rationale**:

- Fine-grained permission control
- Dynamic permission assignment
- Audit trail for access control changes
- Integration with AdonisJS ecosystem
- Scalable across multiple admin users

**Permission Format**: `action:resource` (e.g., `create:product`, `edit:order`)

### Audit Logging

**Decision**: Comprehensive audit logging for all critical operations

**Rationale**:

- Compliance and regulatory requirements
- Security incident investigation
- Business intelligence and analytics
- User behavior tracking
- System debugging and monitoring

**Implementation**: AdonisJS Auditing package with custom resolvers

## Monitoring and Observability

### Multi-layered Monitoring

**Decision**: Implement monitoring at application, infrastructure, and business levels

**Rationale**:

- Proactive issue detection
- Performance optimization insights
- Business metrics tracking
- User experience monitoring

**Tools**:

- CloudWatch for infrastructure monitoring
- Application performance monitoring
- Business metrics dashboards
- Error tracking and alerting

## Trade-offs and Future Considerations

### Current Limitations

1. **Shopify Dependency**: Heavy reliance on Shopify for core e-commerce functions
2. **Monolithic Architecture**: Single application handling multiple business domains
3. **Cost Scaling**: AI and managed service costs scale with usage
4. **Complexity**: Multiple integrations increase system complexity

### Migration Paths

1. **Microservices**: Potential future split into domain-specific services
2. **Multi-tenant Architecture**: Support for multiple brand instances
3. **Global Scaling**: CDN and edge deployment strategies
4. **Advanced AI**: Custom model training and deployment

### Decision Review Process

Technical decisions are reviewed quarterly to assess:

- Performance against original goals
- Emerging technology alternatives
- Scaling challenges and solutions
- Team productivity and developer experience
- Business requirement evolution

This living document ensures technical decisions remain aligned with business objectives and technical best practices.
