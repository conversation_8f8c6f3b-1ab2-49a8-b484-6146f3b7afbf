# Patterns and Conventions

## Overview

This document outlines the coding patterns, architectural conventions, and best practices used throughout the Zurno API codebase. Following these conventions ensures consistency, maintainability, and team productivity.

## Naming Conventions

### Database Entities

#### Table Names

- **Prefix**: All tables use `zn_` prefix
- **Format**: Snake_case (e.g., `zn_users`, `zn_product_variants`)
- **Pluralization**: Always plural for entity tables
- **Junction Tables**: Format `zn_parent_child` (e.g., `zn_users_like_posts`)

```sql
-- Examples
zn_users
zn_products
zn_product_variants
zn_affiliate_commissions
zn_users_like_posts (junction table)
```

#### Column Names

- **Format**: Snake_case in database, camelCase in models
- **Foreign Keys**: `{entity}Id` format (e.g., `userId`, `productId`)
- **Timestamps**: `createdAt`, `updatedAt`, `deletedAt`
- **Boolean Fields**: Clear true/false meaning (e.g., `isActive`, `isVerified`)

```typescript
// Model properties (camelCase)
class ZnProduct extends AppModel {
  @column({ columnName: 'shopify_product_id' })
  declare shopifyProductId: string

  @column({ columnName: 'is_active' })
  declare isActive: boolean
}
```

#### Primary Keys

- **Type**: UUID strings for all entities
- **Column**: `id` (standard across all tables)
- **Rationale**: Global uniqueness, security, compatibility with Shopify IDs

### Model Classes

#### Model Names

- **Prefix**: `Zn` prefix for all business models
- **Format**: PascalCase
- **Naming**: Singular entity name

```typescript
// Examples
ZnUser
ZnProduct
ZnAffiliate
ZnProductVariant
ZnAffiliateCommission
```

#### Model Properties

- **Declared Properties**: Always use `declare` keyword
- **Column Mapping**: Map database snake_case to camelCase
- **Type Safety**: Explicit typing for all properties

```typescript
export default class ZnUser extends AppModel {
  @column({ columnName: 'first_name' })
  declare firstName: string

  @column({ columnName: 'last_name' })
  declare lastName: string

  @column({ columnName: 'shopify_customer_id' })
  declare shopifyCustomerId: string | null
}
```

### File and Class Organization

#### Controllers

- **Format**: PascalCase with `Controller` suffix
- **Location**: `app/controllers/` or `admin/controllers/`
- **Naming**: Resource-based (e.g., `ProductController`, `UserController`)

```typescript
// app/controllers/product_controller.ts
export default class ProductController {
  async index({ response }: HttpContext) {}
  async store({ request, response }: HttpContext) {}
  async show({ params, response }: HttpContext) {}
  async update({ params, request, response }: HttpContext) {}
  async destroy({ params, response }: HttpContext) {}
}
```

#### Services

- **Format**: PascalCase with `Service` suffix
- **Location**: `app/services/`
- **Grouping**: Related services in subdirectories

```typescript
// app/services/auth/auth_service.ts
export class AuthService {
  async createUser(data: CreateUserData) {}
  async sendLoginMail(user: ZnUser) {}
}
```

#### Validators

- **Format**: camelCase with `Validator` suffix
- **Location**: `app/validators/` or `admin/validators/`
- **Naming**: Action-based (e.g., `createProductValidator`)

```typescript
// app/validators/product/create_product_validator.ts
export const createProductValidator = vine.compile(
  vine.object({
    title: vine.string().minLength(1),
    description: vine.string().optional(),
    price: vine.number().min(0),
  })
)
```

#### Jobs

- **Format**: PascalCase with `Job` suffix
- **Location**: `app/jobs/`
- **Naming**: Action-based (e.g., `SyncProductsJob`)

```typescript
// app/jobs/sync_products_job.ts
export default class SyncProductsJob extends BaseJob {
  async handle(payload: SyncProductsPayload) {}
}
```

## Architectural Patterns

### MVC (Model-View-Controller) Pattern

#### Controller Responsibilities

- HTTP request/response handling
- Input validation using VineJS validators
- Delegating business logic to services
- Authentication and authorization
- Response formatting

```typescript
export default class ProductController {
  private productService: ProductService

  constructor() {
    this.productService = new ProductService()
  }

  async store({ request, response, auth }: HttpContext) {
    // 1. Validate input
    const payload = await request.validateUsing(createProductValidator)

    // 2. Get authenticated user
    const user = auth.getUserOrFail()

    // 3. Delegate to service
    const product = await this.productService.createProduct(payload, user)

    // 4. Return response
    return response.created(product)
  }
}
```

#### Service Layer Pattern

- Business logic encapsulation
- Reusable across multiple controllers
- Database operations coordination
- External API integrations

```typescript
export class ProductService {
  async createProduct(data: CreateProductData, user: ZnUser): Promise<ZnProduct> {
    // Business logic here
    const product = await ZnProduct.create({
      ...data,
      userId: user.id,
    })

    // Trigger background jobs
    await this.scheduleProductSync(product)

    return product
  }

  private async scheduleProductSync(product: ZnProduct) {
    await Queue.dispatch(SyncProductsJob, { productId: product.id })
  }
}
```

### Repository Pattern (Implicit through Lucid ORM)

#### Model Methods

- Static methods for queries
- Instance methods for actions
- Relationship definitions

```typescript
export default class ZnUser extends AppModel {
  // Relationship definitions
  @hasMany(() => ZnPost, { foreignKey: 'userId' })
  declare posts: HasMany<typeof ZnPost>

  // Static query methods
  static async findByEmail(email: string) {
    return this.query().where('email', email).first()
  }

  // Instance methods
  async updateLastLogin() {
    this.lastLoginAt = DateTime.now()
    await this.save()
  }
}
```

### Dependency Injection Pattern

#### Service Injection

```typescript
@inject()
export default class AuthController {
  constructor(
    private firebaseService: FirebaseService,
    private authService: AuthService,
    private shopifyAuthService: ShopifyAuthService
  ) {}
}
```

## API Design Patterns

### RESTful Resource Routes

#### Standard CRUD Operations

```typescript
// GET /v1/products - List products
// POST /v1/products - Create product
// GET /v1/products/:id - Show product
// PUT /v1/products/:id - Update product
// DELETE /v1/products/:id - Delete product
```

#### Nested Resources

```typescript
// GET /v1/products/:productId/variants
// POST /v1/products/:productId/variants
// GET /v1/products/:productId/variants/:id
```

#### Non-standard Actions

```typescript
// POST /v1/products/:id/publish
// POST /v1/products/:id/archive
// GET /v1/products/:id/reviews
```

### Response Formatting

#### Success Responses

```typescript
// 200 OK - Resource retrieval
return response.ok(data)

// 201 Created - Resource creation
return response.created(data)

// 204 No Content - Successful action without response body
return response.noContent()
```

#### Error Responses

```typescript
// 400 Bad Request
return response.badRequest({ message: 'Invalid request' })

// 404 Not Found
return response.notFound({ message: 'Resource not found' })

// 422 Validation Error
return response.status(422).send({
  message: 'Validation failed',
  errors: validationErrors,
})
```

### Pagination Pattern

```typescript
// Standard pagination response
{
  data: [...],
  meta: {
    total: 150,
    perPage: 20,
    currentPage: 1,
    lastPage: 8,
    firstPage: 1,
    firstPageUrl: '/?page=1',
    lastPageUrl: '/?page=8',
    nextPageUrl: '/?page=2',
    previousPageUrl: null
  }
}
```

## Security Patterns

### Authentication Guards

#### Guard Usage

```typescript
// Single guard
router
  .group(() => {
    // Admin-only routes
  })
  .use(middleware.auth({ guards: ['jwt_admin'] }))

// Multiple guards
router
  .group(() => {
    // Admin or user routes
  })
  .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] }))
```

#### Authorization Patterns

```typescript
// Controller-level authorization
async store({ auth, bouncer }: HttpContext) {
  await bouncer.authorize('allow', 'create:product')
  // Controller logic
}

// Service-level authorization
async createProduct(user: ZnUser, data: CreateProductData) {
  if (!await bouncer.allows(user, 'create:product')) {
    throw new AuthorizationException()
  }
  // Service logic
}
```

### Input Validation Pattern

#### Validator Structure

```typescript
export const createUserValidator = vine.compile(
  vine.object({
    // Required fields
    email: vine.string().email(),
    firstName: vine.string().minLength(1),
    lastName: vine.string().minLength(1),

    // Optional fields
    phone: vine.string().optional(),
    avatar: vine.string().url().optional(),

    // Nested objects
    address: vine
      .object({
        street: vine.string(),
        city: vine.string(),
        zipCode: vine.string(),
      })
      .optional(),
  })
)
```

## Database Patterns

### Relationship Definitions

#### One-to-Many

```typescript
// Parent model
@hasMany(() => ZnPost, { foreignKey: 'userId' })
declare posts: HasMany<typeof ZnPost>

// Child model
@belongsTo(() => ZnUser, { foreignKey: 'userId' })
declare user: BelongsTo<typeof ZnUser>
```

#### Many-to-Many

```typescript
@manyToMany(() => ZnProduct, {
  pivotTable: 'zn_product_collections',
  pivotForeignKey: 'collectionId',
  pivotRelatedForeignKey: 'productId'
})
declare products: ManyToMany<typeof ZnProduct>
```

#### Conditional Relationships

```typescript
@hasMany(() => ZnPost, {
  foreignKey: 'userId',
  onQuery(query) {
    query.whereNull('deletedAt')
    query.where('status', 'published')
  }
})
declare publishedPosts: HasMany<typeof ZnPost>
```

### Query Patterns

#### Eager Loading

```typescript
const users = await ZnUser.query().preload('posts').preload('addresses').where('active', true)
```

#### Conditional Queries

```typescript
const query = ZnProduct.query()

if (filters.category) {
  query.where('categoryId', filters.category)
}

if (filters.priceMin) {
  query.where('price', '>=', filters.priceMin)
}

const products = await query.paginate(page, limit)
```

### Migration Patterns

#### Standard Migration Structure

```typescript
export default class extends BaseSchema {
  protected tableName = 'zn_products'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().notNullable()
      table.string('title').notNullable()
      table.text('description').nullable()
      table.decimal('price', 10, 2).notNullable()
      table.uuid('vendor_id').nullable()
      table.boolean('is_active').defaultTo(true)

      // Timestamps
      table.timestamp('created_at', { useTz: true }).notNullable()
      table.timestamp('updated_at', { useTz: true }).notNullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()

      // Foreign keys
      table.foreign('vendor_id').references('id').inTable('zn_vendors')

      // Indexes
      table.index(['title'])
      table.index(['vendor_id'])
      table.index(['is_active'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
```

## Background Job Patterns

### Job Structure

```typescript
export default class EmailNotificationJob extends BaseJob {
  async handle(payload: EmailPayload) {
    try {
      await this.sendEmail(payload)
    } catch (error) {
      // Log error and potentially retry
      throw error
    }
  }

  private async sendEmail(payload: EmailPayload) {
    // Email sending logic
  }
}
```

### Job Dispatching

```typescript
// Immediate dispatch
await Queue.dispatch(EmailNotificationJob, { userId: user.id })

// Delayed dispatch
await Queue.dispatch(
  EmailNotificationJob,
  { userId: user.id },
  {
    delay: DateTime.now().plus({ minutes: 5 }).toMillis(),
  }
)

// Queue-specific dispatch
await Queue.dispatch(EmailNotificationJob, payload, {
  queueName: 'notification',
})
```

## Error Handling Patterns

### Custom Exception Classes

```typescript
export class ProductNotFoundException extends Exception {
  static status = 404
  static code = 'PRODUCT_NOT_FOUND'

  constructor(productId: string) {
    super(`Product with ID ${productId} not found`)
  }
}
```

### Error Handler Usage

```typescript
// In controllers
async show({ params, response }: HttpContext) {
  const product = await ZnProduct.find(params.id)

  if (!product) {
    throw new ProductNotFoundException(params.id)
  }

  return response.ok(product)
}
```

## Testing Patterns

### Test Structure

```typescript
test.group('Product Controller', (group) => {
  group.each.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  test('should create product', async ({ client, assert }) => {
    const payload = {
      title: 'Test Product',
      price: 29.99,
    }

    const response = await client.post('/v1/products').json(payload).bearerToken(authToken)

    response.assertStatus(201)
    assert.properties(response.body(), ['id', 'title', 'price'])
  })
})
```

## Code Organization Best Practices

### File Structure

```
app/
├── controllers/          # HTTP controllers
│   ├── admin/           # Admin-specific controllers
│   └── app/             # User-facing controllers
├── models/              # Lucid models
├── services/            # Business logic services
│   ├── auth/           # Authentication services
│   ├── shopify/        # Shopify integration
│   └── chatbot/        # AI services
├── validators/          # VineJS validators
├── jobs/               # Background jobs
├── middleware/         # HTTP middleware
└── exceptions/         # Custom exceptions
```

### Import Conventions

```typescript
// External imports first
import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'

// Internal imports
import ZnUser from '#models/zn_user'
import { AuthService } from '#services/auth_service'
import { createUserValidator } from '#validators/user/create_user_validator'
```

### Documentation Patterns

#### Method Documentation

```typescript
/**
 * @summary Create new user account
 * @description Creates a new user account with email verification
 * @requestBody CreateUserRequest
 * @responseBody 201 - User - Successfully created user
 * @responseBody 422 - ValidationError - Validation failed
 */
async store({ request, response }: HttpContext) {
  // Implementation
}
```

## Performance Patterns

### Caching Strategy

```typescript
// Service-level caching
async getPopularProducts(): Promise<ZnProduct[]> {
  const cacheKey = 'popular_products'

  let products = await Cache.get(cacheKey)
  if (!products) {
    products = await ZnProduct.query()
      .where('isPopular', true)
      .orderBy('sales', 'desc')
      .limit(10)

    await Cache.set(cacheKey, products, '1h')
  }

  return products
}
```

### Pagination Implementation

```typescript
async index({ request, response }: HttpContext) {
  const page = request.input('page', 1)
  const limit = request.input('limit', 20)

  const products = await ZnProduct.query()
    .where('status', 'active')
    .paginate(page, limit)

  return response.ok(products)
}
```

These patterns and conventions ensure consistency across the Zurno API codebase, making it easier for developers to understand, maintain, and extend the system.
