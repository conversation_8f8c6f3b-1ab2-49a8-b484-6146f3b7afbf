# Data Flow

## Overview

This document describes how data flows through the Zurno API system, covering request processing, background job workflows, webhook handling, and third-party integration patterns.

## Core Data Flow Patterns

### 1. Request-Response Flow

#### Standard API Request Flow

```mermaid
sequenceDiagram
    participant Client
    participant LoadBalancer
    participant API as AdonisJS API
    participant Auth as Auth Middleware
    participant Controller
    participant Service
    participant DB as MySQL
    participant Cache as Redis

    Client->>LoadBalancer: HTTP Request
    LoadBalancer->>API: Route Request
    API->>Auth: Authenticate
    Auth->>Cache: Check Session
    Cache-->>Auth: Session Data
    Auth->>Controller: Authorized Request
    Controller->>Service: Business Logic
    Service->>DB: Query Data
    DB-->>Service: Result Set
    Service->>Cache: Cache Result
    Service-->>Controller: Processed Data
    Controller-->>API: Response Data
    API-->>LoadBalancer: HTTP Response
    LoadBalancer-->>Client: Final Response
```

#### Admin Authentication Flow

```mermaid
sequenceDiagram
    participant Admin
    participant API
    participant JWTGuard as jwt_admin Guard
    participant ZnAdmin as Admin Model
    participant Bouncer
    participant DB as MySQL

    Admin->>API: POST /v1/admin/auth/login
    API->>ZnAdmin: Validate Credentials
    ZnAdmin->>DB: Query Admin Record
    DB-->>ZnAdmin: Admin Data
    ZnAdmin-->>API: Validated Admin
    API->>JWTGuard: Generate JWT Token
    JWTGuard-->>API: JWT Token
    API-->>Admin: Login Response + Token

    Note over Admin,DB: Subsequent Requests
    Admin->>API: Authenticated Request
    API->>JWTGuard: Verify Token
    JWTGuard->>Bouncer: Check Permissions
    Bouncer->>DB: Query Permissions
    DB-->>Bouncer: Permission Data
    Bouncer-->>API: Access Decision
    API-->>Admin: Authorized Response
```

### 2. E-commerce Data Flow

#### Order Processing Flow

```mermaid
graph TB
    subgraph "Customer Journey"
        BROWSE[Browse Products]
        CART[Add to Cart]
        CHECKOUT[Checkout]
    end

    subgraph "Zurno API Processing"
        DRAFT[Create Draft Order]
        VALIDATE[Validate Inventory]
        AFFILIATE[Check Affiliate Code]
        SHOPIFY_ORDER[Create Shopify Order]
    end

    subgraph "Background Processing"
        WEBHOOK[Shopify Webhook]
        SYNC_JOB[Sync Order Job]
        COMMISSION[Calculate Commission]
        NOTIFICATION[Send Notifications]
        VENDOR_EARNING[Calculate Vendor Earnings]
    end

    subgraph "External Systems"
        SHOPIFY[(Shopify)]
        EMAIL[Email Service]
        SMS[SMS Service]
        PUSH[Push Notifications]
    end

    BROWSE --> CART
    CART --> CHECKOUT
    CHECKOUT --> DRAFT
    DRAFT --> VALIDATE
    VALIDATE --> AFFILIATE
    AFFILIATE --> SHOPIFY_ORDER
    SHOPIFY_ORDER --> SHOPIFY

    SHOPIFY --> WEBHOOK
    WEBHOOK --> SYNC_JOB
    SYNC_JOB --> COMMISSION
    SYNC_JOB --> VENDOR_EARNING
    SYNC_JOB --> NOTIFICATION

    NOTIFICATION --> EMAIL
    NOTIFICATION --> SMS
    NOTIFICATION --> PUSH
```

#### Affiliate Commission Flow

```mermaid
sequenceDiagram
    participant Customer
    participant API
    participant Shopify
    participant Queue
    participant AffiliationService
    participant Commission
    participant DB

    Customer->>API: Apply Referral Code
    API->>Shopify: Create Discount Code
    Shopify-->>API: Discount Applied
    Customer->>Shopify: Complete Order
    Shopify->>API: Webhook: Order Created
    API->>Queue: Dispatch Sync Job
    Queue->>AffiliationService: Process Order
    AffiliationService->>DB: Query Affiliate Data
    DB-->>AffiliationService: Affiliate + Tier Info
    AffiliationService->>Commission: Calculate Commission
    Commission->>DB: Store Commission Record
    AffiliationService->>DB: Update Affiliate Stats
    AffiliationService-->>Queue: Job Complete
```

### 3. AI Chatbot Data Flow

#### Multi-Agent Processing Flow

```mermaid
graph TB
    subgraph "User Input"
        USER_MSG[User Message]
        ROOM[Chat Room]
    end

    subgraph "Orchestrator Layer"
        QUEUE_JOB[ChatBot Message Job]
        ORCHESTRATOR[Orchestrator Agent]
        ROUTING{Route Decision}
    end

    subgraph "Specialized Agents"
        SHOPPING[Shopping Assistant]
        POST[Post Assistant]
        ORDER[Order Assistant]
        CUSTOMER[Customer Service]
    end

    subgraph "AI Infrastructure"
        OPENAI[OpenAI API]
        PINECONE[Pinecone Vector DB]
        THREAD[OpenAI Thread]
    end

    subgraph "Post-Processing"
        VECTOR_SEARCH[Vector Search]
        RECOMMENDATION[Recommendation Engine]
        CONTEXT_INJECTION[Context Injection]
    end

    subgraph "Response Delivery"
        IVS_CHAT[IVS Chat Service]
        CHAT_MESSAGE[Chat Message]
    end

    USER_MSG --> ROOM
    ROOM --> QUEUE_JOB
    QUEUE_JOB --> ORCHESTRATOR
    ORCHESTRATOR --> ROUTING

    ROUTING --> SHOPPING
    ROUTING --> POST
    ROUTING --> ORDER
    ROUTING --> CUSTOMER

    SHOPPING --> OPENAI
    POST --> OPENAI
    ORDER --> OPENAI
    CUSTOMER --> OPENAI

    OPENAI --> THREAD
    THREAD --> VECTOR_SEARCH
    VECTOR_SEARCH --> PINECONE
    PINECONE --> RECOMMENDATION
    RECOMMENDATION --> CONTEXT_INJECTION

    CONTEXT_INJECTION --> IVS_CHAT
    IVS_CHAT --> CHAT_MESSAGE
```

#### AI Assistant Context Flow

```mermaid
sequenceDiagram
    participant User
    participant Orchestrator
    participant Agent as Specialized Agent
    participant OpenAI
    participant Pinecone
    participant RecommendationEngine
    participant Database

    User->>Orchestrator: "Looking for nail polish"
    Orchestrator->>Agent: Route to Shopping Assistant
    Agent->>OpenAI: Initial Query Processing
    OpenAI-->>Agent: Structured Response

    Note over Agent: Check if post-processing needed
    Agent->>Pinecone: Vector Search for Products
    Pinecone-->>Agent: Similar Product Embeddings
    Agent->>RecommendationEngine: Generate Recommendations
    RecommendationEngine->>Database: Query Product Details
    Database-->>RecommendationEngine: Product Information
    RecommendationEngine-->>Agent: Curated Product List

    Agent->>OpenAI: Context-Enhanced Query
    OpenAI-->>Agent: Final Response with Context
    Agent-->>User: Personalized Recommendations
```

### 4. Live Streaming Data Flow

#### Stream Creation and Management

```mermaid
sequenceDiagram
    participant Admin
    participant API
    participant AWS_IVS
    participant Database
    participant Queue
    participant Viewers

    Admin->>API: Create Stream Post
    API->>AWS_IVS: Create IVS Channel
    AWS_IVS-->>API: Channel ARN + Stream Key
    API->>Database: Store Stream Metadata

    Admin->>AWS_IVS: Start Broadcasting
    AWS_IVS->>API: Stream State Change
    API->>Queue: Update Stream Status
    Queue->>Database: Update Stream Record

    Viewers->>AWS_IVS: Join Stream
    AWS_IVS->>API: Viewer Events
    API->>Database: Track Viewer Analytics

    Viewers->>AWS_IVS: Chat Messages
    AWS_IVS->>API: Chat Message Webhook
    API->>Queue: Process Chat Message
    Queue->>Database: Store Chat Record
```

#### Live Commerce Integration

```mermaid
graph LR
    subgraph "Live Stream"
        STREAMER[Content Creator]
        VIEWERS[Viewers]
        CHAT[Chat Interface]
    end

    subgraph "AI Shopping Assistant"
        CHATBOT[AI Assistant]
        PRODUCT_REC[Product Recommendations]
        SEARCH[Product Search]
    end

    subgraph "E-commerce Integration"
        PRODUCTS[Product Catalog]
        CART[Shopping Cart]
        CHECKOUT[Checkout Process]
    end

    VIEWERS --> CHAT
    CHAT --> CHATBOT
    CHATBOT --> PRODUCT_REC
    PRODUCT_REC --> SEARCH
    SEARCH --> PRODUCTS
    PRODUCTS --> CART
    CART --> CHECKOUT

    STREAMER --> VIEWERS
    CHATBOT --> CHAT
```

### 5. Webhook Data Flow

#### Shopify Webhook Processing

```mermaid
sequenceDiagram
    participant Shopify
    participant Webhook as Webhook Controller
    participant Queue
    participant SyncService
    participant Database
    participant NotificationService

    Note over Shopify: Order Events
    Shopify->>Webhook: POST /webhook/shopify
    Webhook->>Queue: Dispatch Sync Job
    Queue->>SyncService: Process Order Update
    SyncService->>Database: Update Order Record

    Note over SyncService: Commission Calculation
    SyncService->>Database: Query Affiliate Data
    Database-->>SyncService: Affiliate Information
    SyncService->>Database: Create/Update Commission

    Note over SyncService: Vendor Earnings
    SyncService->>Database: Calculate Vendor Earnings

    Note over SyncService: Notifications
    SyncService->>NotificationService: Send Order Notifications
    NotificationService->>Database: Log Notification
```

### 6. Background Job Processing

#### Queue System Data Flow

```mermaid
graph TB
    subgraph "Job Sources"
        HTTP_REQ[HTTP Requests]
        WEBHOOKS[Webhook Events]
        CRON[Scheduled Tasks]
        INTERNAL[Internal Triggers]
    end

    subgraph "Queue System"
        REDIS_QUEUE[(Redis Queue)]
        JOB_DISPATCHER[Job Dispatcher]
        WORKERS[Queue Workers]
    end

    subgraph "Job Categories"
        SYNC_JOBS[Sync Jobs]
        AI_JOBS[AI Processing]
        NOTIFICATION_JOBS[Notifications]
        ORDER_JOBS[Order Processing]
        WEBHOOK_JOBS[Webhook Processing]
    end

    subgraph "External Services"
        SHOPIFY_API[Shopify API]
        OPENAI_API[OpenAI API]
        EMAIL_SERVICE[Email Service]
        SMS_SERVICE[SMS Service]
        PUSH_SERVICE[Push Notifications]
    end

    HTTP_REQ --> JOB_DISPATCHER
    WEBHOOKS --> JOB_DISPATCHER
    CRON --> JOB_DISPATCHER
    INTERNAL --> JOB_DISPATCHER

    JOB_DISPATCHER --> REDIS_QUEUE
    REDIS_QUEUE --> WORKERS

    WORKERS --> SYNC_JOBS
    WORKERS --> AI_JOBS
    WORKERS --> NOTIFICATION_JOBS
    WORKERS --> ORDER_JOBS
    WORKERS --> WEBHOOK_JOBS

    SYNC_JOBS --> SHOPIFY_API
    AI_JOBS --> OPENAI_API
    NOTIFICATION_JOBS --> EMAIL_SERVICE
    NOTIFICATION_JOBS --> SMS_SERVICE
    NOTIFICATION_JOBS --> PUSH_SERVICE
```

### 7. Social Features Data Flow

#### Post Creation and Engagement

```mermaid
sequenceDiagram
    participant User
    participant API
    participant MediaService
    participant S3
    participant Database
    participant NotificationService
    participant VectorService
    participant Pinecone

    User->>API: Create Post with Media
    API->>MediaService: Process Media Upload
    MediaService->>S3: Store Media Files
    S3-->>MediaService: Media URLs
    MediaService-->>API: Media Metadata
    API->>Database: Store Post Record

    Note over API: Generate Embeddings
    API->>VectorService: Create Post Embeddings
    VectorService->>Pinecone: Store Vector Data

    Note over API: Engagement Processing
    User->>API: Like/Comment on Post
    API->>Database: Store Engagement
    API->>NotificationService: Notify Post Owner
    NotificationService->>Database: Log Notification
```

### 8. Data Synchronization Patterns

#### Real-time vs Batch Processing

```mermaid
graph TB
    subgraph "Real-time Data Flow"
        REAL_TIME[Real-time Events]
        WEBHOOKS_RT[Webhook Processing]
        IMMEDIATE[Immediate Processing]
        LIVE_UPDATES[Live Updates]
    end

    subgraph "Batch Processing"
        SCHEDULED[Scheduled Jobs]
        BULK_SYNC[Bulk Synchronization]
        ANALYTICS[Analytics Processing]
        REPORTS[Report Generation]
    end

    subgraph "Hybrid Processing"
        CRITICAL[Critical Operations]
        QUEUE_PRIORITY[Priority Queues]
        FALLBACK[Fallback Mechanisms]
    end

    REAL_TIME --> WEBHOOKS_RT
    WEBHOOKS_RT --> IMMEDIATE
    IMMEDIATE --> LIVE_UPDATES

    SCHEDULED --> BULK_SYNC
    BULK_SYNC --> ANALYTICS
    ANALYTICS --> REPORTS

    CRITICAL --> QUEUE_PRIORITY
    QUEUE_PRIORITY --> FALLBACK
```

## Data Consistency Patterns

### 1. Transaction Management

- **Database Transactions**: Critical operations use database transactions
- **Saga Pattern**: Long-running processes use compensation patterns
- **Eventual Consistency**: Background jobs ensure eventual consistency
- **Conflict Resolution**: Last-write-wins with audit trails

### 2. Cache Invalidation

```mermaid
graph LR
    UPDATE[Data Update]
    DB[Database]
    CACHE[Redis Cache]
    INVALIDATE[Cache Invalidation]
    REFRESH[Cache Refresh]

    UPDATE --> DB
    DB --> INVALIDATE
    INVALIDATE --> CACHE
    CACHE --> REFRESH
```

### 3. Error Handling and Retry Logic

- **Queue Retry**: Failed jobs are retried with exponential backoff
- **Dead Letter Queue**: Permanently failed jobs are moved to DLQ
- **Circuit Breaker**: External service failures trigger circuit breakers
- **Graceful Degradation**: System continues operating with reduced functionality

## Performance Optimization

### 1. Caching Strategy

- **Response Caching**: API responses cached in Redis
- **Database Query Caching**: Expensive queries cached
- **Static Asset Caching**: CDN for media and static files
- **Session Caching**: User sessions stored in Redis

### 2. Database Optimization

- **Read Replicas**: Read operations distributed across replicas
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Indexed queries and query optimization
- **Batch Operations**: Bulk operations for efficiency

This data flow documentation provides a comprehensive view of how information moves through the Zurno API system, ensuring efficient processing, consistency, and scalability across all business domains.
