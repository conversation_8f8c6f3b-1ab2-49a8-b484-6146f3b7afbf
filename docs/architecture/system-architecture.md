# System Architecture

## Overview

Zurno API is a comprehensive B2B beauty supply platform built with AdonisJS v6, serving salon owners and beauty professionals across the United States. The system combines e-commerce, social media, affiliate marketing, AI assistance, and appointment booking into a unified platform.

## Technology Stack

### Backend Framework

- **AdonisJS v6** - Node.js MVC framework with TypeScript
- **Node.js >= 20.x** - Runtime environment
- **TypeScript** - Primary programming language

### Database & Storage

- **MySQL with Read Replicas** - Primary database with scaling architecture
- **Redis** - Caching, session storage, and queue management
- **Lucid ORM** - Database abstraction and relationship management
- **AWS S3** - File and media storage via Flydrive

### Authentication & Security

- **JWT Authentication** - Custom JWT guard implementation
- **Bouncer RBAC** - Role-based access control with database-stored permissions
- **Dual Authentication Guards**:
  - `jwt_admin` - Administrative user authentication
  - `jwt_user` - Customer authentication

### External Integrations

- **Shopify** - E-commerce platform integration for products, orders, and customers
- **OpenAI** - Multi-agent AI assistant system
- **Pinecone** - Vector database for AI-powered recommendations
- **Firebase** - Push notifications and social authentication
- **AWS Services**:
  - **S3** - File storage
  - **IVS** - Interactive video streaming for live shopping
- **Twilio** - SMS and voice services
- **Google Services** - Maps, geocoding, and authentication

### Queue & Job Processing

- **Bull Queue** - Redis-backed job processing
- **Multiple Specialized Queues**:
  - `default` - General background tasks
  - `webhook` - External webhook processing
  - `syncData` - Data synchronization jobs
  - `notification` - Push notification delivery
  - `chatBot` - AI assistant message processing
  - `event-registrations` - Event management
  - `tracking` - Analytics and user behavior tracking

## System Components

### Core Application Layer

```
┌─────────────────────────────────────────────────────────────────┐
│                        AdonisJS Application                      │
├─────────────────────────────────────────────────────────────────┤
│  Controllers  │  Services  │  Models  │  Validators  │  Jobs    │
│  ├─ Admin     │  ├─ Auth   │  ├─ User │  ├─ Input   │  ├─ Sync │
│  ├─ API       │  ├─ Shop   │  ├─ Order│  ├─ Business│  ├─ AI   │
│  └─ Socket    │  └─ AI     │  └─ Post │  └─ API     │  └─ Queue│
└─────────────────────────────────────────────────────────────────┘
```

### Data Layer

```
┌─────────────────────────────────────────────────────────────────┐
│                         Data Storage                             │
├─────────────────────────────────────────────────────────────────┤
│  MySQL (Primary)     │  MySQL (Read Replica)  │  Redis Cache   │
│  ├─ User Data        │  ├─ Reporting Queries   │  ├─ Sessions  │
│  ├─ Orders           │  ├─ Analytics           │  ├─ Cache     │
│  ├─ Products         │  └─ Search Operations   │  └─ Queues    │
│  └─ Transactions     │                         │               │
└─────────────────────────────────────────────────────────────────┘
```

### External Service Layer

```
┌─────────────────────────────────────────────────────────────────┐
│                      External Services                           │
├─────────────────────────────────────────────────────────────────┤
│  Shopify     │  OpenAI      │  Pinecone    │  AWS      │  Others │
│  ├─ Products │  ├─ GPT-4    │  ├─ Vectors  │  ├─ S3    │  ├─ Twilio│
│  ├─ Orders   │  ├─ Agents   │  ├─ Search   │  ├─ IVS   │  ├─ Firebase│
│  └─ Customers│  └─ Embeddings│ └─ ML       │  └─ SES   │  └─ Google│
└─────────────────────────────────────────────────────────────────┘
```

## Architecture Patterns

### Domain-Driven Design

The system is organized around business domains:

- **E-commerce Domain**: Products, orders, inventory, payments
- **Social Domain**: Posts, stores, users, engagement
- **Affiliate Domain**: Commissions, tiers, referrals, payouts
- **AI Domain**: Chatbot agents, recommendations, vector search
- **Appointment Domain**: Booking, services, salon management

### Service Layer Pattern

Business logic is encapsulated in service classes:

- Controllers handle HTTP requests and delegate to services
- Services contain business rules and coordinate between models
- Models represent data and relationships
- Validators ensure data integrity

### Event-Driven Architecture

The system uses events and listeners for:

- Order processing workflows
- Notification delivery
- Data synchronization
- Audit logging
- Real-time updates

### CQRS Pattern

Read and write operations are optimized separately:

- **Write Operations**: Primary MySQL database
- **Read Operations**: Read replicas and Redis cache
- **Analytics**: Separate tracking database
- **Search**: Pinecone vector database

## Scalability Considerations

### Database Scaling

- **Read Replicas**: Distribute read operations across multiple databases
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Indexes and optimized queries for performance
- **Data Partitioning**: Logical separation of domains

### Caching Strategy

- **Application Cache**: Redis for frequently accessed data
- **Session Storage**: Redis-based session management
- **Query Caching**: Automatic caching of expensive database operations
- **CDN Integration**: AWS CloudFront for static assets

### Queue Processing

- **Horizontal Scaling**: Multiple queue workers for parallel processing
- **Queue Prioritization**: Different queues for different priority levels
- **Error Handling**: Retry mechanisms and dead letter queues
- **Monitoring**: Queue health and performance metrics

### API Design

- **RESTful Architecture**: Consistent API design patterns
- **Versioning**: `/v1/`, `/v2/` prefixes for API evolution
- **Rate Limiting**: Protect against abuse and ensure fair usage
- **Documentation**: Swagger/OpenAPI specifications

## Security Architecture

### Authentication Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │    │   API       │    │  Database   │
│  (App/Web)  │    │  Gateway    │    │   & Redis   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                  │                  │
       │  1. Login Req    │                  │
       ├─────────────────→│                  │
       │                  │  2. Validate     │
       │                  ├─────────────────→│
       │                  │  3. User Data    │
       │                  │←─────────────────┤
       │  4. JWT Token    │                  │
       │←─────────────────┤                  │
       │                  │                  │
       │  5. API Request  │                  │
       │     + JWT        │                  │
       ├─────────────────→│                  │
       │                  │  6. Verify JWT   │
       │                  │  & Check Perms   │
       │                  ├─────────────────→│
       │                  │  7. Response     │
       │                  │←─────────────────┤
       │  8. API Response │                  │
       │←─────────────────┤                  │
```

### Permission System

- **Role-Based Access Control (RBAC)**
- **Database-Stored Permissions**: Dynamic permission management
- **Resource-Action Format**: `action:resource` permission structure
- **Hierarchical Roles**: Admin, manager, user role hierarchy
- **Granular Controls**: Fine-grained permission system

## Monitoring & Observability

### Logging Strategy

- **Application Logs**: Structured logging with context
- **Access Logs**: HTTP request/response logging
- **Error Tracking**: Centralized error collection and alerting
- **Audit Trails**: User action tracking for compliance

### Performance Monitoring

- **Database Performance**: Query analysis and optimization
- **API Response Times**: Endpoint performance tracking
- **Queue Processing**: Job execution monitoring
- **External Service Latency**: Third-party service monitoring

### Health Checks

- **Database Connectivity**: Regular health checks
- **Redis Availability**: Cache service monitoring
- **External Services**: Shopify, OpenAI, AWS service status
- **Queue Health**: Job processing status

## Deployment Architecture

### Environment Strategy

- **Development**: Local development with Docker
- **Staging**: Production-like environment for testing
- **Production**: High-availability deployment with load balancing

### Infrastructure

- **Load Balancers**: Distribute traffic across application instances
- **Application Servers**: Multiple Node.js instances with PM2
- **Database Cluster**: Primary-replica MySQL setup
- **Redis Cluster**: High-availability cache and queue backend

### Backup & Recovery

- **Database Backups**: Regular automated backups
- **File Storage**: S3 with versioning and lifecycle management
- **Disaster Recovery**: Cross-region backup strategy
- **Data Retention**: Compliance-aware data lifecycle management

## AI System Architecture

### Multi-Agent AI System

The platform implements a sophisticated multi-agent AI system powered by OpenAI and Pinecone:

```
┌─────────────────────────────────────────────────────────────────┐
│                    AI Assistant Orchestrator                     │
├─────────────────────────────────────────────────────────────────┤
│                     Agent Router                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
│  │  Shopping   │  │    Post     │  │   Order     │  │Customer  ││
│  │   Agent     │  │   Agent     │  │   Agent     │  │ Service  ││
│  │             │  │             │  │             │  │  Agent   ││
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────────┘│
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Vector Search System                          │
├─────────────────────────────────────────────────────────────────┤
│  Pinecone Vector DB     │  Embedding Generation  │  Similarity   │
│  ├─ Product Embeddings  │  ├─ OpenAI Text         │  ├─ Search    │
│  ├─ Post Embeddings     │  ├─ Product Data        │  ├─ Ranking   │
│  ├─ Collection Data     │  ├─ User Content        │  └─ Results   │
│  └─ User Interactions   │  └─ Historical Data     │               │
└─────────────────────────────────────────────────────────────────┘
```

### AI Agent Specifications

#### Shopping Agent

- **Purpose**: Product discovery and recommendations
- **Capabilities**:
  - Natural language product search
  - Contextual recommendations based on user history
  - Price comparison and availability checks
  - Cross-selling and upselling suggestions
- **Integration**: Shopify product catalog, user purchase history

#### Post Agent

- **Purpose**: Social content discovery and engagement
- **Capabilities**:
  - Content recommendation based on interests
  - Trend analysis and popular content identification
  - User-generated content insights
  - Social engagement optimization
- **Integration**: Post database, user interaction data

#### Order Agent

- **Purpose**: Order support and customer service
- **Capabilities**:
  - Order status inquiries and updates
  - Shipping and delivery information
  - Return and refund assistance
  - Invoice and receipt generation
- **Integration**: Order management system, Shopify orders

#### Customer Service Agent

- **Purpose**: General inquiries and platform support
- **Capabilities**:
  - Account management assistance
  - Platform navigation help
  - Feature explanations and tutorials
  - Escalation to human support
- **Integration**: User accounts, help documentation

### Vector Database Strategy

#### Embedding Types

- **Product Embeddings**: Title, description, category, tags
- **Post Embeddings**: Content, captions, hashtags, engagement data
- **Collection Embeddings**: Curated product groupings
- **User Behavior**: Purchase patterns, interaction history

#### Search Algorithms

- **Semantic Similarity**: Vector cosine similarity for content matching
- **Hybrid Search**: Combining vector search with traditional filters
- **Personalization**: User-specific ranking and filtering
- **Real-time Updates**: Dynamic embedding updates via background jobs

## Queue Processing Architecture

### Queue System Design

```
┌─────────────────────────────────────────────────────────────────┐
│                     Bull Queue System                            │
├─────────────────────────────────────────────────────────────────┤
│   Queue Types    │    Processors     │    Job Categories        │
│                  │                   │                          │
│  ┌─────────────┐ │ ┌─────────────┐  │ ┌─────────────────────┐  │
│  │   default   │ │ │  Worker 1   │  │ │   E-commerce Jobs   │  │
│  │   webhook   │ │ │  Worker 2   │  │ │ ├─ Order Sync       │  │
│  │  syncData   │ │ │  Worker 3   │  │ │ ├─ Inventory Update │  │
│  │notification│ │ │  Worker N   │  │ │ └─ Payment Process  │  │
│  │  chatBot    │ │ │             │  │ │                     │  │
│  │  tracking   │ │ │             │  │ │   AI/ML Jobs        │  │
│  │   events    │ │ │             │  │ │ ├─ Vector Updates   │  │
│  └─────────────┘ │ └─────────────┘  │ │ ├─ Embeddings Gen   │  │
│                  │                   │ │ └─ Chatbot Process │  │
│                  │                   │ │                     │  │
│                  │                   │ │   Communication     │  │
│                  │                   │ │ ├─ Email/SMS       │  │
│                  │                   │ │ ├─ Push Notify     │  │
│                  │                   │ │ └─ Campaign Delivery│  │
└─────────────────────────────────────────────────────────────────┘
```

### Background Job Categories

#### E-commerce Processing

- **Order Synchronization**: Shopify webhook processing
- **Inventory Management**: Stock level updates and alerts
- **Vendor Operations**: Multi-vendor order separation
- **Payment Processing**: Transaction handling and reconciliation
- **Fulfillment Tracking**: Shipping and delivery updates

#### AI & Machine Learning

- **Vector Store Updates**: Product and content embedding generation
- **Chatbot Processing**: AI agent message handling
- **Recommendation Engine**: Personalized content and product suggestions
- **Content Analysis**: Post and product content processing
- **Embedding Synchronization**: Real-time vector database updates

#### Communication & Notifications

- **Email Campaigns**: Marketing and transactional emails
- **SMS Notifications**: Order updates and alerts
- **Push Notifications**: Mobile app notifications
- **Live Stream Alerts**: Real-time streaming notifications
- **Appointment Reminders**: Salon booking notifications

#### Data Synchronization

- **Customer Data Sync**: Shopify customer integration
- **Product Catalog Updates**: Real-time product information
- **Social Media Integration**: Instagram and social platform sync
- **Analytics Processing**: User behavior and performance metrics
- **Third-party Integration**: External service data synchronization

### Queue Performance Optimization

#### Job Prioritization

- **Critical**: Payment processing, order fulfillment
- **High**: Customer notifications, security alerts
- **Medium**: Content processing, recommendations
- **Low**: Analytics, bulk data operations

#### Scaling Strategy

- **Horizontal Scaling**: Multiple worker processes
- **Queue Separation**: Domain-specific queues for isolation
- **Retry Logic**: Exponential backoff for failed jobs
- **Dead Letter Queues**: Failed job analysis and recovery

## Integration Architecture

### Shopify Integration

```
┌─────────────────────────────────────────────────────────────────┐
│                     Shopify Integration                          │
├─────────────────────────────────────────────────────────────────┤
│  Webhook Events     │  API Operations      │  Data Sync         │
│                     │                      │                    │
│  ┌─────────────┐   │ ┌─────────────┐     │ ┌─────────────┐    │
│  │Order Created│   │ │Product Mgmt │     │ │Customer Sync│    │
│  │Order Updated│   │ │Inventory    │     │ │Order Sync   │    │
│  │Customer     │   │ │Customer API │     │ │Product Sync │    │
│  │Product      │   │ │Draft Orders │     │ │Inventory    │    │
│  │Inventory    │   │ │Discounts    │     │ │Pricing      │    │
│  └─────────────┘   │ └─────────────┘     │ └─────────────┘    │
└─────────────────────────────────────────────────────────────────┘
```

### AWS Services Integration

#### S3 Storage Architecture

- **Media Bucket**: User-generated content (posts, avatars)
- **Document Storage**: MSDS sheets, product documentation
- **Backup Storage**: Database and application backups
- **CDN Distribution**: CloudFront for global content delivery

#### Interactive Video Streaming (IVS)

- **Live Streaming**: Real-time product demonstrations
- **Recording**: Stream recording and playback
- **Viewer Analytics**: Real-time engagement metrics
- **Chat Integration**: Live interaction features

### Firebase Integration

- **Authentication**: Social login (Google, Facebook)
- **Push Notifications**: Mobile app notifications
- **Analytics**: User behavior tracking
- **Remote Config**: Feature flag management

## Security Architecture Details

### Data Protection

- **Encryption at Rest**: Database and file storage encryption
- **Encryption in Transit**: TLS for all API communications
- **Key Management**: AWS KMS for encryption key management
- **PII Protection**: Personal data anonymization and masking

### API Security

- **Rate Limiting**: Request throttling per user/IP
- **CORS Configuration**: Cross-origin request management
- **Input Validation**: Comprehensive data sanitization
- **SQL Injection Prevention**: Parameterized queries and ORM usage

### Authentication Security

- **JWT Token Management**: Secure token generation and validation
- **Session Management**: Redis-based session storage
- **Password Security**: Bcrypt hashing with salt
- **Two-Factor Authentication**: Email verification for sensitive operations

This comprehensive architecture supports a high-performance, scalable B2B platform capable of serving thousands of beauty professionals with real-time AI assistance, comprehensive e-commerce functionality, and robust social features.
