# User Flows

## Overview

This document outlines the key user flows and customer journeys within the Zurno API platform. The platform serves multiple user types with distinct workflows and business objectives.

## User Types & Personas

### 1. End Customers

- **Goal**: Purchase beauty products for personal use
- **Behavior**: Browse, compare, purchase, review
- **Authentication**: Email verification or social login

### 2. Salon Owners

- **Goal**: Manage store presence and book appointments
- **Behavior**: Claim store, manage services, accept bookings
- **Authentication**: Email verification + store verification

### 3. Beauty Distributors

- **Goal**: Purchase products for resale (B2B)
- **Behavior**: Bulk ordering, inventory management
- **Authentication**: Business account verification

### 4. Affiliates

- **Goal**: Earn commissions through referrals
- **Behavior**: Share products, track performance, receive payouts
- **Authentication**: Affiliate program application and approval

### 5. Content Creators

- **Goal**: Share beauty content and build following
- **Behavior**: Create posts, engage with community, stream
- **Authentication**: Social login preferred

## Core User Flows

### 1. Customer Registration & Authentication

#### Email Verification Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Landing   │    │   Sign Up   │    │ Verification│
│    Page     │───→│    Form     │───→│   Email     │
└─────────────┘    └─────────────┘    └─────────────┘
                          │                   │
                          ▼                   ▼
                   ┌─────────────┐    ┌─────────────┐
                   │ Email Code  │    │  Account    │
                   │  Request    │───→│  Verified   │
                   └─────────────┘    └─────────────┘
```

**Steps:**

1. User provides email and basic info
2. System sends verification code via email
3. User enters code from email
4. Account is activated and user is logged in
5. Optional profile completion

**API Endpoints:**

- `POST /v1/app/auth/send-login-code` - Send verification code
- `POST /v1/app/auth/verify-login-code` - Verify code and login

#### Social Login Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Landing   │    │   Social    │    │  Firebase   │
│    Page     │───→│   Login     │───→│     Auth    │
└─────────────┘    └─────────────┘    └─────────────┘
                          │                   │
                          ▼                   ▼
                   ┌─────────────┐    ┌─────────────┐
                   │   Account   │    │   Profile   │
                   │   Created   │───→│  Complete   │
                   └─────────────┘    └─────────────┘
```

**Steps:**

1. User selects social login (Google/Facebook)
2. Firebase handles authentication
3. System creates or links account
4. User completes profile if needed

**API Endpoints:**

- `POST /v1/app/auth/login` - Social login with Firebase token

### 2. Product Discovery & Shopping

#### Browse & Search Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Home     │    │   Browse    │    │   Product   │
│   Screen    │───→│ Categories  │───→│   Details   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ AI Assistant│    │   Search    │    │  Add to     │
│   Search    │    │  Results    │    │    Cart     │
└─────────────┘    └─────────────┘    └─────────────┘
```

**Product Discovery Methods:**

1. **Category Browsing**: Navigate through product categories
2. **Collection Browsing**: Curated product collections
3. **AI-Powered Search**: Natural language product search
4. **Recommendations**: Personalized "For You" products
5. **Social Discovery**: Products featured in posts

**API Endpoints:**

- `GET /v1/products` - Browse products
- `GET /v1/app/shop/categories` - Product categories
- `GET /v1/app/shop/collections` - Product collections
- `GET /v1/app/shop/for-you-products` - Personalized recommendations
- `POST /v1/chat-bot` - AI assistant search

#### Shopping Cart Management

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Product    │    │    Cart     │    │   Cart      │
│   Page      │───→│   Added     │───→│ Management  │
└─────────────┘    └─────────────┘    └─────────────┘
                          │                   │
                          ▼                   ▼
                   ┌─────────────┐    ┌─────────────┐
                   │  Continue   │    │  Proceed    │
                   │  Shopping   │    │ to Checkout │
                   └─────────────┘    └─────────────┘
```

**Cart Features:**

- Multiple cart sections (organize by vendor/category)
- Quantity adjustments
- Variant selections
- Save for later functionality
- Apply discount codes

**API Endpoints:**

- `GET /v1/app/cart` - Get cart contents
- `POST /v1/app/cart/items` - Add item to cart
- `PUT /v1/app/cart/items/:id` - Update cart item
- `DELETE /v1/app/cart/items/:id` - Remove cart item

### 3. Checkout & Order Processing

#### Checkout Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Cart     │    │  Shipping   │    │   Payment   │
│   Review    │───→│  Address    │───→│   Method    │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Affiliate  │    │   Order     │    │   Order     │
│   Codes     │    │  Summary    │───→│ Confirmation│
└─────────────┘    └─────────────┘    └─────────────┘
```

**Checkout Process:**

1. **Cart Review**: Verify items and quantities
2. **Address Selection**: Choose shipping/billing addresses
3. **Shipping Options**: Select delivery method and speed
4. **Payment Method**: Choose payment option
5. **Discount Application**: Apply affiliate codes or coupons
6. **Order Confirmation**: Review and submit order
7. **Shopify Processing**: Order processed through Shopify

**API Endpoints:**

- `POST /v1/app/checkout` - Process checkout
- `GET /v1/app/profile/addresses` - Get user addresses
- `POST /v1/app/profile/addresses` - Add new address

#### Order Tracking Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Order     │    │   Order     │    │  Shipping   │
│ Confirmation│───→│ Processing  │───→│   Updates   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Email/SMS   │    │  Fulfillment│    │  Delivery   │
│Notifications│    │   Tracking  │    │ Confirmation│
└─────────────┘    └─────────────┘    └─────────────┘
```

**Order Statuses:**

- **Pending**: Order received, awaiting processing
- **Processing**: Order being prepared
- **Shipped**: Order shipped with tracking
- **Delivered**: Order successfully delivered
- **Cancelled**: Order cancelled (before shipping)

**API Endpoints:**

- `GET /v1/app/orders` - Get user orders
- `GET /v1/app/orders/:id` - Get order details
- `POST /v1/app/orders/:id/cancel` - Cancel order

### 4. Social & Content Features

#### Post Creation Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Create    │    │   Media     │    │   Caption   │
│    Post     │───→│   Upload    │───→│ & Tags Add  │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Product    │    │    Post     │    │   Share     │
│   Tagging   │    │  Preview    │───→│  Published  │
└─────────────┘    └─────────────┘    └─────────────┘
```

**Post Features:**

- Image/video upload to AWS S3
- Product tagging for shopping integration
- Store association for store owners
- Category selection for discovery
- AI-powered content suggestions

**API Endpoints:**

- `POST /v1/post` - Create post
- `GET /v1/post` - Browse posts
- `GET /v1/post/:id` - Get post details
- `POST /v1/post/like` - Like/unlike post

#### Social Engagement Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Browse    │    │   Post      │    │  Engage     │
│   Posts     │───→│  Details    │───→│(Like/Comment│
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Follow     │    │   Share     │    │  Profile    │
│   Users     │    │    Post     │    │    View     │
└─────────────┘    └─────────────┘    └─────────────┘
```

**Engagement Features:**

- Like posts and comments
- Comment on posts with replies
- Share posts to social media
- Follow content creators
- Favorite posts for later

### 5. Store Management & Appointments

#### Store Claim Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Search    │    │   Claim     │    │  Verify     │
│   Store     │───→│   Store     │───→│ Ownership   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Store     │    │    Admin    │    │   Store     │
│ Not Listed? │    │  Approval   │    │  Activated  │
└─────────────┘    └─────────────┘    └─────────────┘
```

**Store Claim Process:**

1. Search for existing store location
2. Request store claim with verification docs
3. Admin reviews and approves claim
4. Store owner gains management access
5. Setup services and appointment booking

**API Endpoints:**

- `GET /v1/store` - Search stores
- `POST /v1/admin/stores/:id/verify` - Verify store (admin)

#### Appointment Booking Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Select    │    │   Choose    │    │   Select    │
│   Store     │───→│  Service    │───→│    Time     │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Booking   │    │Confirmation │    │  Reminder   │
│  Details    │───→│   & Payment │    │Notifications│
└─────────────┘    └─────────────┘    └─────────────┘
```

**Appointment Features:**

- Service catalog management
- Availability calendar
- Customer booking interface
- Payment processing
- Reminder notifications
- Cancellation/rescheduling

**API Endpoints:**

- `GET /v1/app/store/store-service/services` - List services
- `GET /v1/app/appointments` - Get appointments
- `POST /v1/app/appointments` - Book appointment

### 6. Affiliate Marketing

#### Affiliate Registration Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Register   │    │  Submit     │    │   Admin     │
│ Application │───→│ Documents   │───→│   Review    │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Approval   │    │  Tier       │    │  Referral   │
│Notification │───→│ Assignment  │───→│Code Creation│
└─────────────┘    └─────────────┘    └─────────────┘
```

**Affiliate Onboarding:**

1. Complete application form
2. Submit required documentation
3. Admin review and tier assignment
4. Approval notification
5. Referral code generation
6. Access to affiliate dashboard

#### Affiliate Marketing Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Share     │    │ Customer    │    │   Order     │
│  Products   │───→│  Clicks     │───→│  Tracking   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Commission  │    │   Payout    │    │  Payment    │
│Calculation  │───→│ Processing  │───→│  Received   │
└─────────────┘    └─────────────┘    └─────────────┘
```

**Commission Process:**

1. Affiliate shares product with referral code
2. Customer uses code during checkout
3. Order is tracked to affiliate
4. Commission calculated based on tier
5. Monthly payout processing
6. Payment to affiliate's account

### 7. AI Assistant Interactions

#### AI Chatbot Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   User      │    │    Agent    │    │   Vector    │
│   Query     │───→│  Routing    │───→│   Search    │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Specialized │    │   Response  │    │   Follow    │
│   Agent     │───→│ Generation  │───→│     Up      │
└─────────────┘    └─────────────┘    └─────────────┘
```

**AI Agent Types:**

1. **Shopping Agent**: Product recommendations and search
2. **Post Agent**: Content discovery and social features
3. **Order Agent**: Order status and support
4. **Customer Service**: General inquiries and support

**AI Capabilities:**

- Natural language product search
- Personalized recommendations
- Order status inquiries
- General customer support
- Vector similarity search

**API Endpoints:**

- `POST /v1/chat-bot` - Send message to AI assistant
- `GET /v1/chat-bot/history` - Get conversation history

### 8. Live Streaming & Media

#### Live Stream Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Start     │    │    AWS      │    │   Viewer    │
│  Stream     │───→│  IVS Setup  │───→│   Join      │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Product    │    │ Engagement  │    │   Stream    │
│ Showcase    │───→│  Tracking   │───→│     End     │
└─────────────┘    └─────────────┘    └─────────────┘
```

**Live Commerce Features:**

- Product demonstrations
- Real-time viewer interaction
- Shopping integration during stream
- Viewer count tracking
- Stream recording and playback

## Cross-Platform Flows

### Mobile App Integration

- Push notifications for orders, posts, streams
- Offline browsing capabilities
- Camera integration for post creation
- Location services for store discovery

### Web Application

- Desktop-optimized interfaces
- Advanced search and filtering
- Bulk operations for B2B users
- Analytics dashboards

### Admin Dashboard

- Order management and fulfillment
- Content moderation
- User management
- Analytics and reporting

## Error Handling & Edge Cases

### Network Issues

- Offline data caching
- Request retry mechanisms
- Graceful degradation

### Authentication Errors

- Token refresh flows
- Re-authentication prompts
- Session timeout handling

### Payment Failures

- Payment retry options
- Alternative payment methods
- Cart preservation

### Inventory Issues

- Out-of-stock notifications
- Alternative product suggestions
- Backorder handling

## User Experience Considerations

### Performance Optimization

- Lazy loading for large lists
- Image optimization and CDN
- Caching for frequently accessed data
- Pagination for search results

### Accessibility

- Screen reader compatibility
- Keyboard navigation
- High contrast options
- Text size adjustments

### Personalization

- ML-powered recommendations
- Customized content feeds
- Location-based services
- Purchase history integration

### Notification Strategy

- Order status updates
- Product restocks
- Appointment reminders
- Social engagement notifications
- Promotional campaigns

## Analytics & Tracking

### User Behavior Tracking

- Page views and time spent
- Click-through rates
- Conversion funnels
- Abandonment points

### Business Metrics

- Sales performance
- Customer acquisition costs
- Affiliate conversion rates
- Social engagement rates

### Performance Monitoring

- API response times
- Error rates
- User session data
- System health metrics
