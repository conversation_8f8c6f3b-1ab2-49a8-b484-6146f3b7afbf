# Development Workflow

## Overview

This document outlines the development workflow, coding standards, and best practices for the Zurno API project. The platform follows established AdonisJS patterns with specific business requirements for the beauty supply industry.

## Development Environment Setup

### Prerequisites

- **Node.js >= 20.x** - Runtime environment
- **MySQL 8.0+** - Primary database
- **Redis 6.0+** - Caching and queue management
- **Git** - Version control

### Environment Configuration

```bash
# Clone repository
git clone <repository-url>
cd zurno-api

# Install dependencies
npm install

# Environment setup
cp .env.example .env
# Configure database, Redis, and third-party API keys

# Database setup
node ace migration:run
node ace db:seed
```

### Required Environment Variables

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_DATABASE=zurno_api

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# Authentication
APP_KEY=your-app-key
JWT_SECRET=your-jwt-secret

# Third-party Integrations
SHOPIFY_API_KEY=
SHOPIFY_API_SECRET=
OPENAI_API_KEY=
PINECONE_API_KEY=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
```

## Code Structure & Organization

### Directory Convention

```
zurno-api/
├── app/                    # Core application code
│   ├── controllers/        # HTTP request handlers
│   ├── models/            # Lucid ORM models (zn_ prefix)
│   ├── services/          # Business logic services
│   ├── validators/        # VineJS input validators
│   ├── middleware/        # HTTP middleware
│   ├── jobs/             # Background queue jobs
│   ├── listeners/        # Event listeners
│   └── exceptions/       # Custom exception handlers
├── admin/                 # Admin panel specific code
│   ├── controllers/       # Admin-only controllers
│   ├── validators/        # Admin-specific validators
│   └── route/            # Admin route definitions
├── database/             # Database related files
│   ├── migrations/       # Schema migrations
│   ├── permissions/      # RBAC permission definitions
│   └── seeders/         # Database seeders
├── start/               # Application bootstrap
│   └── route/           # API route definitions
└── docs/                # Documentation
```

### Naming Conventions

#### Database & Models

- **Tables**: `zn_` prefix with snake_case
  - Examples: `zn_users`, `zn_products`, `zn_orders`
- **Models**: PascalCase with `Zn` prefix
  - Examples: `ZnUser`, `ZnProduct`, `ZnOrder`
- **Columns**: camelCase in models, snake_case in database
- **Foreign Keys**: `{entity}Id` format
  - Examples: `userId`, `productId`, `vendorId`
- **Primary Keys**: UUID format for all entities

#### Files & Classes

- **Controllers**: PascalCase + `Controller` suffix
  - Examples: `ProductController`, `UserController`
- **Services**: PascalCase + `Service` suffix
  - Examples: `UserService`, `ProductService`
- **Validators**: camelCase + `Validator` suffix
  - Examples: `createUserValidator`, `updateProductValidator`
- **Jobs**: PascalCase + `Job` suffix
  - Examples: `SyncOrderJob`, `SendNotificationJob`

#### API Routes

- **Versioning**: `/v1/`, `/v2/` prefixes
- **Admin Routes**: `/v1/admin/` prefix
- **App Routes**: `/v1/app/` prefix (authenticated users)
- **Public Routes**: `/v1/` prefix (no authentication)

## Development Patterns

### Controller Pattern

```typescript
export default class ExampleController {
  private exampleService: ExampleService

  constructor() {
    this.exampleService = new ExampleService()
  }

  /**
   * @swagger
   * /v1/examples:
   *   get:
   *     summary: List examples
   *     tags: [Examples]
   */
  async index({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)

      const examples = await this.exampleService.list({ page, limit })
      return response.ok(examples)
    } catch (error) {
      return response.internalServerError({
        message: 'Failed to fetch examples',
      })
    }
  }
}
```

### Service Layer Pattern

```typescript
export class ExampleService {
  /**
   * Business logic should be in services, not controllers
   */
  async list(options: ListOptions) {
    const { page, limit } = options

    // Use models for data access
    const examples = await ZnExample.query().whereNull('deletedAt').paginate(page, limit)

    return examples
  }

  async create(data: CreateExampleData) {
    // Validate business rules
    await this.validateBusinessRules(data)

    // Create using transaction
    const trx = await db.transaction()
    try {
      const example = await ZnExample.create(data, { client: trx })
      await trx.commit()
      return example
    } catch (error) {
      await trx.rollback()
      throw error
    }
  }
}
```

### Validation Pattern

```typescript
export const createExampleValidator = vine.compile(
  vine.object({
    title: vine.string().minLength(1).maxLength(255),
    description: vine.string().optional(),
    price: vine.number().positive(),
    tags: vine.array(vine.string()).optional(),
  })
)
```

### Model Relationships

```typescript
export default class ZnExample extends AppModel {
  @belongsTo(() => ZnUser, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof ZnUser>

  @hasMany(() => ZnExampleItem, {
    foreignKey: 'exampleId',
    onQuery(query) {
      query.whereNull('deletedAt')
    },
  })
  declare items: HasMany<typeof ZnExampleItem>
}
```

## Database Development

### Migration Workflow

```bash
# Create migration
node ace make:migration create_example_table

# Create model with migration
node ace make:model ZnExample --migration

# Run migrations
node ace migration:run

# Rollback latest migration
node ace migration:rollback

# Check migration status
node ace migration:status
```

### Migration Best Practices

```typescript
export default class extends BaseSchema {
  protected tableName = 'zn_examples'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.string('title').notNullable()
      table.text('description').nullable()
      table.decimal('price', 10, 2).notNullable()
      table.uuid('userId').references('id').inTable('zn_users')
      table.boolean('active').defaultTo(true)
      table.timestamp('createdAt').notNullable()
      table.timestamp('updatedAt').notNullable()
      table.timestamp('deletedAt').nullable()

      // Indexes
      table.index(['userId'])
      table.index(['active', 'deletedAt'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
```

## Queue & Background Jobs

### Job Development

```typescript
export default class ExampleJob extends BaseJob {
  async handle(payload: ExampleJobPayload) {
    try {
      // Job implementation
      await this.processExample(payload.exampleId)
    } catch (error) {
      // Error handling and retry logic
      throw error
    }
  }

  private async processExample(exampleId: string) {
    // Job logic here
  }
}
```

### Queue Usage

```typescript
// Dispatch job
await Queue.dispatch('ExampleJob', { exampleId: '123' })

// Dispatch to specific queue
await Queue.dispatch('ExampleJob', payload, {
  queueName: 'processing',
})

// Delayed job
await Queue.dispatch('ExampleJob', payload, {
  delay: 60000, // 1 minute delay
})
```

## Testing Standards

### Test Structure

```typescript
test.group('ExampleController', (group) => {
  group.each.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  test('should list examples', async ({ assert, client }) => {
    // Arrange
    const user = await ZnUser.create(userFactory.make())
    const example = await ZnExample.create({
      ...exampleFactory.make(),
      userId: user.id,
    })

    // Act
    const response = await client.get('/v1/examples').loginAs(user)

    // Assert
    response.assertStatus(200)
    assert.include(response.body().data[0].id, example.id)
  })
})
```

### Testing Commands

```bash
# Run all tests
npm test

# Run specific test suite
npm run test:unit
npm run test:functional

# Run tests with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

## API Development

### REST Conventions

- **GET** `/v1/resources` - List resources
- **GET** `/v1/resources/:id` - Get specific resource
- **POST** `/v1/resources` - Create resource
- **PUT** `/v1/resources/:id` - Update entire resource
- **PATCH** `/v1/resources/:id` - Partial update
- **DELETE** `/v1/resources/:id` - Delete resource

### Response Format

```typescript
// Success responses
return response.ok(data) // 200
return response.created(data) // 201

// Error responses
return response.badRequest({
  // 400
  message: 'Invalid request data',
})

return response.status(422).send({
  // 422 Validation Error
  message: 'Validation failed',
  errors: validationErrors,
})
```

### Swagger Documentation

```typescript
/**
 * @swagger
 * /v1/examples:
 *   post:
 *     summary: Create a new example
 *     tags: [Examples]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Example Title"
 *     responses:
 *       201:
 *         description: Example created successfully
 */
```

## Git Workflow

### Branch Strategy

- **main** - Production ready code
- **develop** - Integration branch for features
- **feature/feature-name** - Feature development
- **hotfix/fix-name** - Production hotfixes

### Commit Conventions

```
type(scope): description

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation updates
- style: Code style changes
- refactor: Code refactoring
- test: Test additions/updates
- chore: Build/tooling changes

Examples:
feat(auth): add social login support
fix(orders): resolve payment processing issue
docs(api): update authentication documentation
```

### Pull Request Process

1. Create feature branch from `develop`
2. Implement feature with tests
3. Update documentation if needed
4. Submit pull request to `develop`
5. Code review and approval
6. Merge and deploy

## Code Quality

### Linting & Formatting

```bash
# ESLint check
npm run lint

# Fix linting issues
npm run lint:fix

# Prettier formatting
npm run format

# Type checking
npm run type-check
```

### Pre-commit Hooks

Husky configuration automatically runs:

- Linting
- Type checking
- Test suite
- Formatting

## Deployment Workflow

### Development Environment

```bash
# Start development server
npm run dev

# Start with specific environment
NODE_ENV=development npm run dev
```

### Production Deployment

```bash
# Build application
npm run build

# Start production server
npm start

# PM2 deployment
pm2 start ecosystem.config.js
```

### Environment-Specific Considerations

- **Development**: Debug logging, hot reload
- **Staging**: Production-like environment for testing
- **Production**: Optimized builds, error monitoring, clustering

## Performance Best Practices

### Database Optimization

- Use eager loading for relationships
- Implement proper indexing
- Use read replicas for queries
- Cache expensive operations

### Memory Management

- Clean up event listeners
- Use streams for large data processing
- Implement proper pagination
- Monitor memory usage

### API Performance

- Implement response caching
- Use compression middleware
- Rate limiting for API protection
- CDN for static assets

## Security Guidelines

### Input Validation

- Always validate user inputs
- Use VineJS validators
- Sanitize data before processing
- Implement rate limiting

### Authentication & Authorization

- Use JWT tokens for authentication
- Implement proper RBAC
- Validate permissions at service level
- Regular security audits

### Data Protection

- Hash sensitive data
- Use HTTPS in production
- Implement proper CORS
- Regular dependency updates

## Monitoring & Debugging

### Logging

```typescript
// Use structured logging
logger.info('User created', {
  userId: user.id,
  email: user.email,
})

// Error logging
logger.error('Failed to process order', {
  orderId,
  error: error.message,
})
```

### Health Checks

- Database connectivity
- Redis connectivity
- External service availability
- Queue processing status

### Error Tracking

- Implement global error handler
- Use error tracking service
- Monitor API response times
- Set up alerting for critical issues

## Best Practices Summary

### Development

1. **Follow naming conventions consistently**
2. **Write comprehensive tests**
3. **Use services for business logic**
4. **Implement proper error handling**
5. **Document APIs with Swagger**

### Database

1. **Use migrations for schema changes**
2. **Implement soft deletes**
3. **Use UUIDs for primary keys**
4. **Add proper indexes**
5. **Use transactions for critical operations**

### API Design

1. **Follow RESTful conventions**
2. **Implement proper versioning**
3. **Use consistent response formats**
4. **Add authentication where needed**
5. **Validate all inputs**

### Security

1. **Never expose sensitive data**
2. **Validate and sanitize inputs**
3. **Use proper authentication**
4. **Implement RBAC correctly**
5. **Keep dependencies updated**
