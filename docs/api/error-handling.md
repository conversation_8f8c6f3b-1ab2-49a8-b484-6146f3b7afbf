# Error Handling

## Overview

Zurno API implements comprehensive error handling with consistent error response formats, proper HTTP status codes, and detailed error messages for debugging and user feedback.

## Global Exception Handler

The API uses a centralized exception handler located in `app/exceptions/handler.ts` that processes all errors and returns consistent responses.

### Error Response Format

All error responses follow this consistent structure:

```json
{
  "message": "Human-readable error message",
  "errors": [
    {
      "field": "fieldName",
      "message": "Field-specific error message",
      "rule": "validation rule that failed"
    }
  ],
  "code": "ERROR_CODE",
  "statusCode": 422
}
```

## HTTP Status Codes

### 2xx Success

- `200 OK` - Successful GET, PUT requests
- `201 Created` - Successful POST requests
- `204 No Content` - Successful DELETE requests

### 4xx Client Errors

#### 400 Bad Request

Used for malformed requests or invalid parameters.

```json
{
  "message": "Invalid request parameters",
  "statusCode": 400
}
```

**Common causes:**

- Invalid JSON payload
- Missing required headers
- Malformed URL parameters

#### 401 Unauthorized

Used when authentication is required but not provided or invalid.

```json
{
  "message": "Unauthorized access",
  "statusCode": 401
}
```

**Common causes:**

- Missing Authorization header
- Invalid or expired JWT token
- Token signature verification failed

#### 403 Forbidden

Used when user is authenticated but lacks permissions.

```json
{
  "message": "Access denied",
  "statusCode": 403
}
```

**Common causes:**

- Insufficient role permissions
- Account suspended or deactivated
- Resource access restrictions

#### 404 Not Found

Used when requested resource doesn't exist.

```json
{
  "message": "Resource not found",
  "statusCode": 404
}
```

**Common causes:**

- Invalid resource ID
- Deleted or soft-deleted resource
- Route not found

#### 422 Unprocessable Entity

Used for validation errors.

```json
{
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "The email field is required",
      "rule": "required"
    },
    {
      "field": "password",
      "message": "The password must be at least 8 characters",
      "rule": "minLength"
    }
  ],
  "statusCode": 422
}
```

#### 429 Too Many Requests

Used when rate limits are exceeded.

```json
{
  "message": "Too many requests",
  "statusCode": 429,
  "retryAfter": 60
}
```

### 5xx Server Errors

#### 500 Internal Server Error

Used for unexpected server errors.

```json
{
  "message": "Internal server error",
  "statusCode": 500,
  "requestId": "req-uuid-12345"
}
```

#### 503 Service Unavailable

Used during maintenance or when external services are down.

```json
{
  "message": "Service temporarily unavailable",
  "statusCode": 503,
  "retryAfter": 300
}
```

## Validation Errors

### VineJS Validation

The API uses VineJS for request validation with detailed error messages:

```typescript
// Example validator
export const createUserValidator = vine.compile(
  vine.object({
    email: vine.string().email(),
    firstName: vine.string().minLength(1).maxLength(50),
    lastName: vine.string().minLength(1).maxLength(50),
    phone: vine.string().mobile().optional(),
  })
)
```

### Validation Error Response

```json
{
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "The email field must be a valid email address",
      "rule": "email"
    },
    {
      "field": "firstName",
      "message": "The firstName field must be at least 1 character",
      "rule": "minLength"
    }
  ],
  "statusCode": 422
}
```

## Authentication Errors

### JWT Guard Errors

#### Invalid Token

```json
{
  "message": "Unauthorized access",
  "code": "E_UNAUTHORIZED_ACCESS",
  "statusCode": 401
}
```

#### Token Expired

```json
{
  "message": "Token has expired",
  "code": "E_JWT_TOKEN_EXPIRED",
  "statusCode": 401
}
```

#### User Account Deactivated

```json
{
  "message": "User account is deactivated",
  "code": "E_USER_DEACTIVATED",
  "statusCode": 403
}
```

### Admin vs User Guard Conflicts

```json
{
  "message": "Admin access required",
  "code": "E_ADMIN_ACCESS_REQUIRED",
  "statusCode": 403
}
```

## Business Logic Errors

### E-commerce Errors

#### Insufficient Inventory

```json
{
  "message": "Insufficient inventory",
  "code": "E_INSUFFICIENT_INVENTORY",
  "statusCode": 422,
  "details": {
    "productId": "uuid",
    "requested": 5,
    "available": 2
  }
}
```

#### Order Processing Failed

```json
{
  "message": "Order processing failed",
  "code": "E_ORDER_PROCESSING_FAILED",
  "statusCode": 422,
  "details": {
    "orderId": "uuid",
    "reason": "Payment processing error"
  }
}
```

### Affiliate Program Errors

#### Referral Code Invalid

```json
{
  "message": "Invalid referral code",
  "code": "E_INVALID_REFERRAL_CODE",
  "statusCode": 422,
  "details": {
    "code": "INVALID123",
    "reason": "Code expired"
  }
}
```

#### Commission Calculation Error

```json
{
  "message": "Commission calculation failed",
  "code": "E_COMMISSION_CALCULATION_FAILED",
  "statusCode": 500,
  "details": {
    "orderId": "uuid",
    "affiliateId": "uuid"
  }
}
```

### Social Platform Errors

#### Content Moderation

```json
{
  "message": "Content violates community guidelines",
  "code": "E_CONTENT_VIOLATION",
  "statusCode": 422,
  "details": {
    "violations": ["inappropriate_content", "spam"]
  }
}
```

#### File Upload Errors

```json
{
  "message": "File upload failed",
  "code": "E_FILE_UPLOAD_FAILED",
  "statusCode": 422,
  "details": {
    "reason": "File too large",
    "maxSize": "10MB",
    "actualSize": "15MB"
  }
}
```

## External Service Errors

### Shopify Integration Errors

#### API Rate Limit

```json
{
  "message": "Shopify API rate limit exceeded",
  "code": "E_SHOPIFY_RATE_LIMIT",
  "statusCode": 429,
  "retryAfter": 30
}
```

#### Product Sync Failed

```json
{
  "message": "Product synchronization failed",
  "code": "E_SHOPIFY_SYNC_FAILED",
  "statusCode": 500,
  "details": {
    "shopifyProductId": "12345",
    "reason": "Product not found in Shopify"
  }
}
```

### AI Service Errors

#### OpenAI API Error

```json
{
  "message": "AI service temporarily unavailable",
  "code": "E_AI_SERVICE_ERROR",
  "statusCode": 503,
  "details": {
    "service": "openai",
    "retryAfter": 60
  }
}
```

#### Vector Database Error

```json
{
  "message": "Search service error",
  "code": "E_VECTOR_DB_ERROR",
  "statusCode": 500,
  "details": {
    "service": "pinecone",
    "operation": "query"
  }
}
```

### Firebase Errors

#### Push Notification Failed

```json
{
  "message": "Push notification failed",
  "code": "E_PUSH_NOTIFICATION_FAILED",
  "statusCode": 500,
  "details": {
    "deviceToken": "invalid_token",
    "reason": "Invalid registration token"
  }
}
```

## Database Errors

### Connection Errors

```json
{
  "message": "Database connection failed",
  "code": "E_DATABASE_CONNECTION",
  "statusCode": 500,
  "requestId": "req-uuid-12345"
}
```

### Constraint Violations

```json
{
  "message": "Database constraint violation",
  "code": "E_DATABASE_CONSTRAINT",
  "statusCode": 422,
  "details": {
    "constraint": "unique_email",
    "field": "email"
  }
}
```

### Transaction Errors

```json
{
  "message": "Transaction failed",
  "code": "E_TRANSACTION_FAILED",
  "statusCode": 500,
  "details": {
    "operation": "order_processing",
    "reason": "Deadlock detected"
  }
}
```

## Queue Job Errors

### Job Processing Failed

```json
{
  "message": "Background job failed",
  "code": "E_JOB_FAILED",
  "statusCode": 500,
  "details": {
    "jobType": "SendEmailJob",
    "attempts": 3,
    "maxAttempts": 5
  }
}
```

### Queue Connection Error

```json
{
  "message": "Queue service unavailable",
  "code": "E_QUEUE_UNAVAILABLE",
  "statusCode": 503,
  "retryAfter": 30
}
```

## Error Handling Best Practices

### Client-Side Error Handling

```javascript
// Example client-side error handling
async function handleApiCall() {
  try {
    const response = await fetch('/v1/api/endpoint', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const error = await response.json()

      switch (response.status) {
        case 401:
          // Redirect to login
          redirectToLogin()
          break
        case 422:
          // Display validation errors
          displayValidationErrors(error.errors)
          break
        case 500:
          // Show generic error message
          showErrorMessage('Something went wrong. Please try again.')
          break
        default:
          showErrorMessage(error.message)
      }
      return
    }

    const data = await response.json()
    // Handle success
  } catch (error) {
    // Handle network errors
    showErrorMessage('Network error. Please check your connection.')
  }
}
```

### Retry Logic

For transient errors (5xx, 429), implement exponential backoff:

```javascript
async function retryWithBackoff(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      if (i === maxRetries - 1) throw error

      if (error.status >= 500 || error.status === 429) {
        const delay = Math.pow(2, i) * 1000 // Exponential backoff
        await new Promise((resolve) => setTimeout(resolve, delay))
      } else {
        throw error // Don't retry client errors
      }
    }
  }
}
```

### Error Logging

All errors are automatically logged with:

- Request ID for tracing
- User context (if authenticated)
- Stack traces (in development)
- Error classification and severity

### Monitoring and Alerting

- Critical errors (5xx) trigger immediate alerts
- High error rates trigger monitoring alerts
- Business logic errors are tracked for analysis
- Performance degradation is monitored

## Development and Debugging

### Error Context in Development

In development mode, additional error information is provided:

```json
{
  "message": "Validation failed",
  "errors": [...],
  "statusCode": 422,
  "stack": "Error stack trace...",
  "requestId": "req-uuid-12345",
  "timestamp": "2024-01-01T12:00:00Z",
  "endpoint": "POST /v1/api/users",
  "userAgent": "...",
  "ip": "127.0.0.1"
}
```

### Error Codes Reference

All error codes follow the pattern `E_{CATEGORY}_{SPECIFIC_ERROR}`:

- `E_UNAUTHORIZED_ACCESS` - Authentication required
- `E_INSUFFICIENT_PERMISSIONS` - Authorization failed
- `E_VALIDATION_FAILED` - Request validation failed
- `E_RESOURCE_NOT_FOUND` - Resource doesn't exist
- `E_RATE_LIMIT_EXCEEDED` - Too many requests
- `E_EXTERNAL_SERVICE_ERROR` - Third-party service error
- `E_DATABASE_ERROR` - Database operation failed
- `E_INTERNAL_ERROR` - Unexpected server error

### Custom Exception Classes

The API defines custom exception classes for different error types:

```typescript
class ValidationException extends Exception {
  static status = 422
  static code = 'E_VALIDATION_FAILED'
}

class UnauthorizedException extends Exception {
  static status = 401
  static code = 'E_UNAUTHORIZED_ACCESS'
}

class BusinessLogicException extends Exception {
  static status = 422
  static code = 'E_BUSINESS_LOGIC_ERROR'
}
```

This error handling system ensures consistent, informative error responses that help both developers and end-users understand and resolve issues effectively.
