# API Endpoints Reference

## Overview

Zurno API provides a comprehensive RESTful interface for the multi-domain platform combining e-commerce, social media, affiliate marketing, AI assistance, and appointment booking. The API is versioned and organized into logical groups with proper authentication and authorization.

## Base URL

```
Production: https://api.zurno.com
Development: http://localhost:3333
```

## API Versioning

Current version: **v1**
Base path: `/v1`

Future version: **v2** (in development)
Base path: `/v2`

## Authentication

All authenticated endpoints require a JWT token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Guards

- **`jwt_admin`**: Admin/staff authentication
- **`jwt_user`**: Customer authentication
- **`jwt_admin`, `jwt_user`**: Either admin or user

## Response Format

### Success Response

```json
{
  "data": [...],
  "message": "Success message"
}
```

### Paginated Response

```json
{
  "data": [...],
  "meta": {
    "total": 150,
    "perPage": 20,
    "currentPage": 1,
    "lastPage": 8,
    "firstPage": 1,
    "firstPageUrl": "/?page=1",
    "lastPageUrl": "/?page=8",
    "nextPageUrl": "/?page=2",
    "previousPageUrl": null
  }
}
```

### Error Response

```json
{
  "message": "Error description",
  "errors": [
    {
      "message": "Validation error",
      "rule": "required",
      "field": "fieldName"
    }
  ]
}
```

## Core Endpoints

### General

#### Health Check

```http
GET /v1/
```

**Response:**

```json
"It works! v1"
```

#### Current User

```http
GET /v1/me
```

**Authentication:** Required (`jwt_admin` or `jwt_user`)

**Response:**

```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "name": "User Name"
  // User or Admin object
}
```

## Authentication Endpoints

### Admin Authentication

#### Admin Login

```http
POST /v1/admin/auth/login
```

**Request Body:**

```json
{
  "username": "<EMAIL>",
  "password": "password"
}
```

**Response:**

```json
{
  "type": "bearer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "username": "<EMAIL>",
    "name": "Admin User",
    "isActive": true,
    "isSuperAdmin": false,
    "roles": []
  }
}
```

#### Passby Check-in

```http
POST /v1/admin/auth/passby-checkin
```

**Request Body:**

```json
{
  "code": "admin-id-uuid"
}
```

### User Authentication

#### Send Login Code

```http
POST /v1/app/auth/send-login-code
```

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

#### Verify Login Code

```http
POST /v1/app/auth/verify-login-code
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**Response:**

```json
{
  "type": "bearer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "isActive": true
  }
}
```

#### Social Login

```http
POST /v1/app/auth/social-login
```

**Request Body:**

```json
{
  "provider": "google",
  "idToken": "firebase-id-token",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe"
}
```

#### User Signup

```http
POST /v1/app/auth/signup
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "password": "password",
  "confirmPassword": "password"
}
```

## Product Endpoints

### Public Product Endpoints

#### List Products

```http
GET /v1/products
```

**Query Parameters:**

- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search term
- `category` (string): Category ID
- `vendor` (string): Vendor ID
- `priceFrom` (number): Minimum price
- `priceTo` (number): Maximum price
- `sortBy` (string): Sort field

**Response:**

```json
{
  "data": [
    {
      "id": "uuid",
      "title": "Product Name",
      "price": "29.99",
      "description": "Product description",
      "vendor": {
        "id": "uuid",
        "name": "Vendor Name"
      },
      "image": {
        "id": "uuid",
        "url": "https://cdn.zurno.com/image.jpg"
      },
      "variants": [
        {
          "id": "uuid",
          "title": "Default Title",
          "price": "29.99",
          "sku": "SKU123"
        }
      ]
    }
  ],
  "meta": {
    "total": 100,
    "perPage": 10,
    "currentPage": 1
  }
}
```

#### Product Detail

```http
GET /v1/products/{id}
```

**Response:**

```json
{
  "id": "uuid",
  "title": "Product Name",
  "description": "Detailed description",
  "price": "29.99",
  "vendor": {
    "id": "uuid",
    "name": "Vendor Name"
  },
  "medias": [
    {
      "id": "uuid",
      "url": "https://cdn.zurno.com/image.jpg",
      "type": "image"
    }
  ],
  "variants": [...],
  "reviews": [...],
  "relatedProducts": [...]
}
```

#### Product Reviews

```http
GET /v1/products/{id}/reviews
```

#### Related Products

```http
GET /v1/products/{id}/relate
```

#### Product Videos

```http
GET /v1/products/{id}/videos
```

#### Product Affiliate Info

```http
GET /v1/products/{id}/affiliate-info
```

#### Product Bundles

```http
GET /v1/products/bundles
```

```http
GET /v1/products/bundles-v2
```

```http
GET /v1/products/bundles/{id}
```

### Authenticated Product Endpoints

**Authentication:** Required (`jwt_admin` or `jwt_user`)

#### Create Product

```http
POST /v1/products
```

**Request Body:**

```json
{
  "title": "New Product",
  "description": "Product description",
  "price": "29.99",
  "vendorId": "vendor-uuid",
  "categoryId": "category-uuid"
}
```

#### Update Product

```http
PUT /v1/products/{id}
```

#### Delete Product

```http
DELETE /v1/products/{id}
```

#### Restock Notification

```http
POST /v1/products/restock-notification
```

#### List Vendor Products

```http
GET /v1/products/vendor-products
```

#### List Product Categories

```http
GET /v1/products/categories
```

#### List Product Types

```http
GET /v1/products/types
```

#### List Product Tags

```http
GET /v1/products/tags
```

#### List Vendors

```http
GET /v1/products/vendors
```

## Post/Social Media Endpoints

### Public Post Endpoints

#### List Posts

```http
GET /v1/post
```

**Query Parameters:**

- `page` (number): Page number
- `limit` (number): Items per page
- `search` (string): Search term
- `source` (string): Post source (`zurno`, `reel`)
- `categoryIds` (array): Category IDs
- `storeIds` (array): Store IDs
- `isDraft` (boolean): Draft status
- `isFavourite` (boolean): Favourite status
- `priceFrom` (number): Min price
- `priceTo` (number): Max price
- `latitude` (number): Search latitude
- `longitude` (number): Search longitude
- `miles` (number): Distance limit
- `sortBy` (string): Sort type (`default`, `distance`, `popular`, `newest`)

#### List Videos

```http
GET /v1/post/videos
```

#### Popular Posts

```http
GET /v1/post/popular
```

#### For You Posts

```http
GET /v1/post/for-you
```

#### Next Video

```http
GET /v1/post/nextvideo
```

#### Post Detail

```http
GET /v1/post/{id}
```

#### Post Metadata

```http
GET /v1/post/{id}/metadata
```

#### Related Posts

```http
GET /v1/post/related/{id}
```

### Authenticated Post Endpoints

**Authentication:** Required (`jwt_admin` or `jwt_user`)

#### Create Post

```http
POST /v1/post
```

**Request Body:**

```json
{
  "title": "Post Title",
  "description": "Post description",
  "price": "29.99",
  "categoryIds": ["category-uuid"],
  "mediaIds": ["media-uuid"],
  "storeId": "store-uuid"
}
```

#### Update Post

```http
PUT /v1/post/{id}
```

#### Delete Post

```http
DELETE /v1/post/{id}
```

#### My Posts

```http
GET /v1/post/mine
```

#### Post Statistics

```http
GET /v1/post/statistic
```

### Post Interactions

#### Like Post

```http
POST /v1/post/like
```

**Request Body:**

```json
{
  "postId": "post-uuid"
}
```

#### Dislike Post

```http
POST /v1/post/dislike
```

**Request Body:**

```json
{
  "postId": "post-uuid"
}
```

## Store Endpoints

### Public Store Endpoints

#### List Stores

```http
GET /v1/store
```

#### Popular Stores

```http
GET /v1/store/popular
```

#### Store Detail

```http
GET /v1/store/{id}
```

#### Store Images

```http
GET /v1/store/images/{id}
```

### Authenticated Store Endpoints

**Authentication:** Required (`jwt_admin` or `jwt_user`)

#### My Store

```http
GET /v1/store/mine
```

#### Create Store

```http
POST /v1/store
```

**Request Body:**

```json
{
  "name": "Store Name",
  "description": "Store description",
  "address": "Store address",
  "phone": "+**********",
  "email": "<EMAIL>"
}
```

#### Update Store

```http
PUT /v1/store/{id}
```

#### Delete Store

```http
DELETE /v1/store/{id}
```

#### Claim Store

```http
POST /v1/store/claim
```

#### Verify Store

```http
POST /v1/store/verify
```

#### Sync Instagram

```http
POST /v1/store/sync-instagram
```

#### Enable Booking Management

```http
POST /v1/store/enable-manage-booking
```

#### Disable Booking Management

```http
POST /v1/store/disable-manage-booking
```

## Shopping Endpoints

### Public Shop Endpoints

#### List Categories

```http
GET /v1/app/shop/categories
```

#### Category Detail

```http
GET /v1/app/shop/categories/{id}
```

#### Category Products

```http
GET /v1/app/shop/categories/{id}/products
```

#### List Collections

```http
GET /v1/app/shop/collections
```

#### Collection Detail

```http
GET /v1/app/shop/collections/{id}
```

#### Popular Products

```http
GET /v1/app/shop/most-popular-products
```

#### Flash Sale Products

```http
GET /v1/app/shop/flash-sale-products
```

#### New Arrivals

```http
GET /v1/app/shop/new-arrivals-products
```

#### Best Sellers

```http
GET /v1/app/shop/best-sellers-products
```

#### Shop Menu

```http
GET /v1/app/shop/menu
```

#### Cancel Reasons

```http
GET /v1/app/shop/cancel-reasons
```

### Authenticated Shop Endpoints

**Authentication:** Required (`jwt_user`)

#### For You Products

```http
GET /v1/app/shop/for-you-products
```

## Cart & Checkout Endpoints

**Authentication:** Required (`jwt_user`)

### Cart Management

#### Get Cart

```http
GET /v1/app/cart
```

#### Add to Cart

```http
POST /v1/app/cart
```

**Request Body:**

```json
{
  "productId": "product-uuid",
  "variantId": "variant-uuid",
  "quantity": 1
}
```

#### Update Cart Item

```http
PUT /v1/app/cart/{id}
```

#### Remove from Cart

```http
DELETE /v1/app/cart/{id}
```

### Checkout

#### Create Checkout

```http
POST /v1/app/checkout
```

**Request Body:**

```json
{
  "items": [
    {
      "productId": "product-uuid",
      "variantId": "variant-uuid",
      "quantity": 1
    }
  ],
  "shippingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "address1": "123 Main St",
    "city": "New York",
    "zip": "10001",
    "country": "US"
  },
  "paymentMethod": "stripe"
}
```

## Order Endpoints

**Authentication:** Required (`jwt_user`)

#### List Orders

```http
GET /v1/order
```

#### Order Detail

```http
GET /v1/order/{id}
```

#### Cancel Order

```http
POST /v1/order/{id}/cancel
```

**Request Body:**

```json
{
  "reason": "Changed my mind"
}
```

## User Profile Endpoints

**Authentication:** Required (`jwt_user`)

### Profile Management

#### Get Profile

```http
GET /v1/app/profile
```

#### Update Profile

```http
POST /v1/app/profile
```

**Request Body:**

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+**********",
  "dateOfBirth": "1990-01-01"
}
```

#### Update Locale

```http
POST /v1/app/profile/update-locale
```

**Request Body:**

```json
{
  "locale": "en"
}
```

#### Delete Profile

```http
DELETE /v1/app/profile
```

### Address Management

#### List Addresses

```http
GET /v1/app/profile/addresses
```

#### Create Address

```http
POST /v1/app/profile/addresses
```

**Request Body:**

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "address1": "123 Main St",
  "address2": "Apt 4B",
  "city": "New York",
  "state": "NY",
  "zip": "10001",
  "country": "US",
  "phone": "+**********"
}
```

#### Update Address

```http
PUT /v1/app/profile/addresses/{id}
```

#### Delete Address

```http
DELETE /v1/app/profile/addresses/{id}
```

#### Set Default Address

```http
POST /v1/app/profile/addresses/default
```

**Request Body:**

```json
{
  "addressId": "address-uuid"
}
```

#### Get Default Address

```http
GET /v1/app/profile/addresses/default
```

#### Get Zurno Addresses

```http
GET /v1/app/profile/addresses/zurno
```

## Affiliate Marketing Endpoints

### Public Affiliate Endpoints

#### Get Referral Code

```http
GET /v1/affiliates/refcode/{id}
```

### Authenticated Affiliate Endpoints

**Authentication:** Required (`jwt_admin` or `jwt_user`)

#### Create Affiliate Account

```http
POST /v1/affiliates
```

**Request Body:**

```json
{
  "referralCode": "CUSTOM_CODE",
  "bankAccount": {
    "accountNumber": "**********",
    "routingNumber": "*********",
    "accountHolderName": "John Doe"
  }
}
```

#### Get Affiliate Profile

```http
GET /v1/affiliates
```

#### Update Affiliate Profile

```http
PUT /v1/affiliates
```

#### Update Affiliate Field

```http
PATCH /v1/affiliates
```

#### Affiliate Statistics

```http
GET /v1/affiliates/stats
```

**Response:**

```json
{
  "totalCommissions": "1234.56",
  "totalPayouts": "900.00",
  "pendingCommissions": "334.56",
  "clickCount": 156,
  "conversionRate": "12.5%",
  "currentTier": {
    "id": "uuid",
    "name": "Bronze",
    "commissionRate": "5.0"
  }
}
```

#### List Commissions

```http
GET /v1/affiliates/commissions
```

#### List Payouts

```http
GET /v1/affiliates/payouts
```

#### Video Report

```http
GET /v1/affiliates/video-report
```

### Affiliate Tiers

#### List Tiers

```http
GET /v1/affiliate-tiers
```

#### Tier Detail

```http
GET /v1/affiliate-tiers/{id}
```

#### Next Tier

```http
GET /v1/affiliate-tiers/{id}/next
```

### Payment Methods

#### List Payment Methods

```http
GET /v1/payment-methods
```

#### Create Payment Method

```http
POST /v1/payment-methods
```

**Request Body:**

```json
{
  "type": "paypal",
  "paypal": {
    "email": "<EMAIL>"
  }
}
```

#### Update Payment Method

```http
PUT /v1/payment-methods/{id}
```

#### Delete Payment Method

```http
DELETE /v1/payment-methods/{id}
```

#### Set Default Payment Method

```http
PATCH /v1/payment-methods/{id}/set-default
```

## Vendor Endpoints

### Public Vendor Endpoints

#### Create Vendor

```http
POST /v1/vendors
```

### Authenticated Vendor Endpoints

**Authentication:** Required (`jwt_admin` or `jwt_user`)

#### Get Vendor Profile

```http
GET /v1/vendors
```

#### Update Vendor Profile

```http
PUT /v1/vendors
```

#### Vendor Statistics

```http
GET /v1/vendors/stats
```

#### Vendor Orders

```http
GET /v1/vendors/orders
```

#### Vendor Earnings

```http
GET /v1/vendors/earnings
```

#### Vendor Payments

```http
GET /v1/vendors/payments
```

### Vendor Payment Methods

#### List Vendor Payment Methods

```http
GET /v1/vendors/payment-methods
```

#### Create Vendor Payment Method

```http
POST /v1/vendors/payment-methods
```

#### Set Default Vendor Payment Method

```http
PATCH /v1/vendors/payment-methods/{id}/set-default
```

## AI Assistant/GPT Endpoints

### Translation

#### Translate Text

```http
POST /v1/app/gpt/translation
```

**Request Body:**

```json
{
  "text": "Hello world",
  "translateTo": "vi"
}
```

**Response:**

```json
{
  "text": "Xin chào thế giới",
  "translateTo": "vi",
  "translateFrom": "en"
}
```

### Chat Bot

**Authentication:** Required (`jwt_admin` or `jwt_user`)

#### Chat with AI Assistant

```http
POST /v1/chat-bot
```

**Request Body:**

```json
{
  "message": "I'm looking for red lipstick",
  "sessionId": "session-uuid"
}
```

**Response:**

```json
{
  "response": "I found several red lipsticks that might interest you...",
  "sessionId": "session-uuid",
  "agent": "shopping",
  "suggestions": [
    {
      "type": "product",
      "id": "product-uuid",
      "title": "Ruby Red Lipstick"
    }
  ]
}
```

## Store Services & Appointment Booking

**Authentication:** Required (`jwt_user`)

### Service Management

#### List Services

```http
GET /v1/app/store/store-service/setting/services
```

**Query Parameters:**

- `storeId` (string, required): Store ID
- `page` (number): Page number
- `limit` (number): Items per page
- `categories` (array): Category IDs
- `priceFrom` (number): Minimum price
- `priceTo` (number): Maximum price
- `durationFrom` (number): Minimum duration (minutes)
- `durationTo` (number): Maximum duration (minutes)

#### Create Service

```http
POST /v1/app/store/store-service/setting/services
```

**Request Body:**

```json
{
  "name": "Manicure",
  "description": "Professional manicure service",
  "price": "35.00",
  "duration": 60,
  "categoryIds": ["category-uuid"],
  "storeId": "store-uuid"
}
```

#### Update Service

```http
PUT /v1/app/store/store-service/setting/services/{id}
```

#### Delete Service

```http
DELETE /v1/app/store/store-service/setting/services/{id}
```

### Service Categories

#### List Categories

```http
GET /v1/app/store/store-service/setting/categories
```

#### Create Category

```http
POST /v1/app/store/store-service/setting/categories
```

#### Update Category

```http
PUT /v1/app/store/store-service/setting/categories/{id}
```

#### Delete Category

```http
DELETE /v1/app/store/store-service/setting/categories/{id}
```

### Service Packages

#### List Packages

```http
GET /v1/app/store/store-service/setting/packages
```

#### Create Package

```http
POST /v1/app/store/store-service/setting/packages
```

#### Update Package

```http
PUT /v1/app/store/store-service/setting/packages/{id}
```

#### Delete Package

```http
DELETE /v1/app/store/store-service/setting/packages/{id}
```

### Tax Settings

#### List Tax Settings

```http
GET /v1/app/store/store-service/setting/taxes
```

#### Create Tax Setting

```http
POST /v1/app/store/store-service/setting/taxes
```

#### Update Tax Setting

```http
PUT /v1/app/store/store-service/setting/taxes/{id}
```

#### Delete Tax Setting

```http
DELETE /v1/app/store/store-service/setting/taxes/{id}
```

#### Set Default Tax

```http
PUT /v1/app/store/store-service/setting/taxes/{id}/set-default
```

### Appointments

#### Book Appointment

```http
POST /v1/app/appointment
```

**Request Body:**

```json
{
  "storeId": "store-uuid",
  "serviceIds": ["service-uuid"],
  "appointmentDate": "2024-01-15",
  "appointmentTime": "14:30",
  "customerNotes": "First time customer"
}
```

#### List Appointments

```http
GET /v1/app/appointment
```

#### Appointment Detail

```http
GET /v1/app/appointment/{id}
```

#### Cancel Appointment

```http
DELETE /v1/app/appointment/{id}
```

#### Reschedule Appointment

```http
PUT /v1/app/appointment/{id}
```

## Event Management Endpoints

### Public Event Endpoints

#### List Events

```http
GET /v1/events
```

#### Event Detail

```http
GET /v1/events/{id}
```

#### Registration Page

```http
GET /v1/events/{id}/register
```

#### Confirmation Page

```http
GET /v1/events/confirmation/{id}
```

#### Check-in Page

```http
GET /v1/events/{id}/check-in
```

#### Event Registration

```http
POST /v1/events/{slug}/register
```

**Request Body:**

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+**********"
}
```

#### Self Check-in

```http
POST /v1/events/{id}/check-in
```

#### Resend Email

```http
POST /v1/events/{id}/resend-email
```

#### QR Check-in

```http
POST /v1/events/check-in/qr
```

### Authenticated Event Endpoints

**Authentication:** Required (`jwt_admin` or `jwt_user`)

#### Registration Status

```http
GET /v1/events/registration/status/{jobId}
```

#### Get Registration

```http
GET /v1/events/registration/{id}
```

#### Check Registration by Contact

```http
POST /v1/events/check-registration
```

#### Get Raffle Reward

```http
GET /v1/events/raffles/{id}
```

## Tracking & Analytics Endpoints

#### Track User Activity

```http
POST /v1/trackings
```

**Request Body:**

```json
{
  "eventType": "page_view",
  "eventData": {
    "page": "/product/123",
    "referrer": "https://google.com"
  }
}
```

#### Get Views

```http
POST /v1/trackings/view
```

#### Get Interactions

```http
POST /v1/trackings/interactions
```

#### Get Store Viewers

```http
GET /v1/trackings/store-viewer
```

#### Get Post Viewers

```http
GET /v1/trackings/post-viewer
```

#### Like Resource

```http
POST /v1/trackings/resource/like
```

#### Get Resource Likes

```http
GET /v1/trackings/resource/likes
```

### Search Tracking

#### Track Search

```http
POST /v1/trackings/search
```

**Request Body:**

```json
{
  "query": "red lipstick",
  "resultsCount": 25,
  "category": "makeup"
}
```

#### Search Analytics

```http
GET /v1/trackings/search/analytics
```

#### Popular Search Terms

```http
GET /v1/trackings/search/popular
```

#### Get Location by IP

```http
GET /v1/trackings/location
```

## Chat & Communication Endpoints

### Chat Rooms

#### Create Chat Token

```http
GET /v1/chat/{id}/token
```

#### Send Chat Event

```http
POST /v1/chat/{id}/event
```

#### Get Messages

```http
GET /v1/chat/{id}/messages
```

#### Get Message Children

```http
GET /v1/chat/messages/{id}/children
```

#### Connect to Chat Room

```http
POST /v1/chat/{id}/connection
```

### Authenticated Chat

**Authentication:** Required (`jwt_admin` or `jwt_user`)

#### Create Chat Message

```http
POST /v1/chat/{id}/messages
```

**Request Body:**

```json
{
  "content": "Hello there!",
  "messageType": "text"
}
```

#### Delete Chat Message

```http
DELETE /v1/chat/messages/{id}
```

## Campaign & Marketing Endpoints

**Authentication:** Required (`jwt_admin`)

### Campaign Management

#### List Campaigns

```http
GET /v1/campaign
```

#### Create Campaign

```http
POST /v1/campaign
```

**Request Body:**

```json
{
  "name": "Summer Sale 2024",
  "description": "Summer collection promotion",
  "sendType": "email",
  "targetAudience": {
    "segments": ["new_customers", "high_value"]
  },
  "content": {
    "subject": "Summer Sale - Up to 50% Off!",
    "body": "Don't miss our biggest sale of the year..."
  },
  "scheduledAt": "2024-07-01T10:00:00Z"
}
```

#### Campaign Detail

```http
GET /v1/campaign/{id}
```

#### Update Campaign

```http
PUT /v1/campaign/{id}
```

#### Delete Campaign

```http
DELETE /v1/campaign/{id}
```

#### Send Campaign

```http
POST /v1/campaign/{id}/send
```

## Newsfeed Endpoints

#### Get Newsfeed

```http
POST /v1/newsfeed
```

**Request Body:**

```json
{
  "filterIds": [],
  "locale": "en",
  "latitude": 40.7128,
  "longitude": -74.006,
  "miles": 50
}
```

**Query Parameters:**

- `limit` (number): Record limit (default: 10)
- `miles` (number): Distance in miles
- `latitude` (number): User latitude
- `longitude` (number): User longitude

**Response:**

```json
{
  "data": [
    {
      "type": "post",
      "id": "post-uuid",
      "title": "Amazing new product!",
      "user": {...},
      "store": {...}
    },
    {
      "type": "review",
      "id": "review-uuid",
      "rating": 5,
      "content": "Great product!",
      "product": {...}
    },
    {
      "type": "product",
      "id": "product-uuid",
      "title": "Featured Product",
      "price": "29.99"
    }
  ]
}
```

## Media & File Upload Endpoints

#### Upload Media

```http
POST /v1/media
```

**Request:** Multipart form data

- `file`: The file to upload
- `type`: Media type (`image`, `video`, `document`)

**Response:**

```json
{
  "id": "media-uuid",
  "url": "https://cdn.zurno.com/uploads/file.jpg",
  "type": "image",
  "filename": "file.jpg",
  "size": 1024000
}
```

#### Get Media

```http
GET /v1/media/{id}
```

#### Delete Media

```http
DELETE /v1/media/{id}
```

## Collection Endpoints

#### List Collections

```http
GET /v1/collections
```

#### Collection Detail

```http
GET /v1/collections/{id}
```

#### Collection Products

```http
GET /v1/collections/{id}/products
```

## Translation Endpoints

#### Get Translations

```http
GET /v1/translation
```

**Query Parameters:**

- `locale` (string): Language code (`en`, `vi`, `es`, etc.)
- `namespace` (string): Translation namespace

**Response:**

```json
{
  "locale": "en",
  "translations": {
    "common.hello": "Hello",
    "common.goodbye": "Goodbye",
    "product.title": "Product",
    "product.price": "Price"
  }
}
```

## Webhook Endpoints

### Shopify Webhooks

```http
POST /v1/webhook/shopify
```

**Headers:**

- `X-Shopify-Topic`: Webhook topic
- `X-Shopify-Shop-Domain`: Shop domain
- `X-Shopify-Webhook-Id`: Webhook ID

### Fulfil Webhooks

```http
POST /v1/webhook/fulfil
```

### Stream Update Webhooks

```http
POST /v1/webhook/update-stream
```

## Data Sync Endpoints

#### Sync 1Viet Data

```http
GET /v1/sync-data-1viet
```

## Socket/Real-time Endpoints

#### Initialize Socket Connection

```http
GET /v1/socket/init
```

#### Socket Authentication

```http
POST /v1/socket/auth
```

## Error Status Codes

| Status Code | Description                              |
| ----------- | ---------------------------------------- |
| 200         | OK - Request successful                  |
| 201         | Created - Resource created successfully  |
| 400         | Bad Request - Invalid request data       |
| 401         | Unauthorized - Authentication required   |
| 403         | Forbidden - Insufficient permissions     |
| 404         | Not Found - Resource not found           |
| 422         | Unprocessable Entity - Validation failed |
| 429         | Too Many Requests - Rate limit exceeded  |
| 500         | Internal Server Error - Server error     |

## Rate Limiting

API requests are rate limited to prevent abuse:

- **Authenticated users**: 1000 requests per hour
- **Unauthenticated users**: 100 requests per hour
- **Admin users**: 5000 requests per hour

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1609459200
```

## Pagination

All list endpoints support pagination with the following parameters:

- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)

Pagination metadata is included in response:

```json
{
  "data": [...],
  "meta": {
    "total": 150,
    "perPage": 20,
    "currentPage": 1,
    "lastPage": 8,
    "firstPage": 1,
    "firstPageUrl": "/?page=1",
    "lastPageUrl": "/?page=8",
    "nextPageUrl": "/?page=2",
    "previousPageUrl": null
  }
}
```

## Internationalization

The API supports multiple languages through the `AppLanguage` header:

```http
AppLanguage: en
AppLanguage: vi
AppLanguage: es
```

Supported languages:

- `en` - English (default)
- `vi` - Vietnamese
- `es` - Spanish
- `fr` - French
- `de` - German

## Testing

### Postman Collection

A comprehensive Postman collection is available with pre-configured requests for all endpoints:

```
https://documenter.getpostman.com/view/zurno-api/v1
```

### Environment Variables

Set the following variables in your testing environment:

```javascript
{
  "baseUrl": "https://api.zurno.com/v1",
  "adminToken": "Bearer {{admin_jwt_token}}",
  "userToken": "Bearer {{user_jwt_token}}"
}
```

This comprehensive API documentation covers all major endpoints and functionality available in the Zurno platform, providing developers with the complete reference needed for integration and development.
