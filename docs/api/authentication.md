# Authentication

## Overview

Zurno API uses JWT (JSON Web Token) based authentication with two distinct user types and guards. The system supports email verification login and social login through Firebase/Google. **Important**: All user accounts must exist in Shopify as customers - this is a hard requirement for the platform.

## Authentication Guards

### JWT Guards

The system implements two separate JWT guards:

1. **`jwt_admin`** - For admin/staff users (ZnAdmin model)
2. **`jwt_user`** - For regular users/customers (ZnUser model)

### Configuration

Authentication is configured in `config/auth.ts`:

```typescript
const authConfig = defineConfig({
  default: 'jwt_user',
  guards: {
    jwt_admin: (ctx) => {
      return new JwtGuard(ctx, znAdminUserProvider, jwtConfig)
    },
    jwt_user: (ctx) => {
      return new JwtGuard(ctx, ZnUserProvider, jwtConfig)
    },
  },
})
```

### Custom JWT Guard Features

The custom JWT guard implementation provides:

- Bearer token authentication
- Automatic user status validation (active/inactive)
- Soft delete checking (`deletedAt` field)
- JWT secret using `APP_KEY` from environment

## Admin Authentication

### Login Endpoint

**POST** `/v1/admin/auth/login`

```json
{
  "username": "<EMAIL>",
  "password": "swordfish"
}
```

**Response:**

```json
{
  "type": "bearer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "username": "<EMAIL>",
    "name": "Admin User",
    "isActive": true,
    "isSuperAdmin": false,
    "roles": []
  }
}
```

### Passby Check-in

**POST** `/v1/admin/auth/passby-checkin`

Quick authentication method using admin ID as code.

```json
{
  "code": "admin-id-uuid"
}
```

## User Authentication

### Shopify Customer Requirement

**Critical**: All users must exist as customers in Shopify before they can authenticate. The authentication flow:

1. Checks if user exists in Shopify by email
2. If no Shopify customer found, authentication fails
3. If Shopify customer exists but no Zurno account, creates one automatically
4. Syncs user data between Zurno and Shopify

### Email Verification Login

Users authenticate using a two-step email verification process:

#### Step 1: Request Login Code

**POST** `/v1/app/auth/`

```json
{
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "hasShopify": true
}
```

**Validation Process:**

1. Searches for Shopify customer by email
2. If no Shopify customer found, returns 400 "Invalid credentials"
3. If Shopify customer exists, creates/updates Zurno user account
4. Sends login verification code via email
5. Sets temporary login code with expiration (5 minutes)

#### Step 2: Verify Code

**POST** `/v1/app/auth/verify-login`

```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**Response:**

```json
{
  "auth": {
    "type": "bearer",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "shopifyCustomerId": "gid://shopify/Customer/123",
    "rewardPoints": 150,
    "smileId": 12345,
    "lastLoginAt": "2024-01-15T10:30:00Z",
    "verifiedAt": "2024-01-01T12:00:00Z"
  },
  "shopify": {
    "id": "123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+**********",
    "points": 150,
    "smileId": 12345
  }
}
```

**Post-Login Processing:**

1. Updates `lastLoginAt` timestamp
2. Clears login code and expiration
3. Syncs user data with Shopify (phone, points, etc.)
4. Creates default address if none exists
5. Syncs store ownership relationships

### Social Authentication

**POST** `/v1/app/auth/social`

```json
{
  "idToken": "firebase-id-token"
}
```

**Social Login Flow:**

1. Verifies Firebase ID token
2. Extracts provider info (Google, Facebook, etc.)
3. Generates unique email if missing
4. Checks/creates Shopify customer
5. Creates/updates Zurno user account
6. Links social provider credentials

**Response:** Same as email verification login response.

### User Signup

**POST** `/v1/app/auth/signup`

Creates new user accounts (requires validation).

## Token Usage

### Authorization Header

Include the JWT token in the Authorization header:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Token Payload

JWT tokens contain:

```json
{
  "userId": "user-uuid",
  "iat": **********,
  "exp": **********
}
```

### Current User Endpoint

**GET** `/v1/me`

Returns current authenticated user (works for both admin and user guards):

```
Authorization: Bearer [token]
```

## Security Features

### User Status Validation

The JWT guard automatically validates:

- **Active Status**: `active` or `isActive` must be `true` (not 0)
- **Deletion Status**: `deletedAt` must be `null`
- **Token Validity**: Valid signature and expiration
- **Account Existence**: User must exist in database

### Account Deactivation

If a user account is deactivated or deleted, authentication will fail with:

```json
{
  "message": "User account is deactivated"
}
```

or

```json
{
  "message": "User account is deleted"
}
```

### Login Code Security

- Codes expire after 5 minutes
- Codes are single-use and cleared after verification
- Email-based verification prevents brute force attacks

## Role-Based Access Control (RBAC)

### Bouncer Integration

The system uses AdonisJS Bouncer for authorization:

```typescript
// In controllers
await bouncer.authorize('allow', 'create:post')
```

### Permission Format

Permissions follow the format: `action:resource`

Examples:

- `create:post` - Create posts
- `edit:product` - Edit products
- `view:admin_dashboard` - View admin dashboard
- `manage:affiliate_commission` - Manage affiliate commissions

### Super Admin

Super admin users bypass all permission checks automatically.

## Error Handling

### Common Authentication Errors

- **401 Unauthorized**: Missing or invalid token
- **403 Forbidden**: Valid token but insufficient permissions
- **400 Bad Request**: Invalid credentials or missing Shopify account
- **422 Unprocessable Entity**: Validation errors

### Error Response Format

```json
{
  "message": "Error description",
  "errors": {
    "field": ["Specific validation error"]
  }
}
```

## Integration Details

### Shopify Integration

- All customers must exist in Shopify
- User data synced bi-directionally
- Reward points from Shopify Smile.io
- Address information synchronized

### Firebase Integration

- Social authentication provider
- ID token verification
- Multi-provider support (Google, Facebook, etc.)
- Fallback email generation for providers without email

### Email Service

- Login code delivery via email
- SMTP configuration in environment
- Template-based email system

## Best Practices

### Token Management

- Store tokens securely (not in localStorage for sensitive data)
- Implement token refresh if needed
- Handle token expiration gracefully

### API Usage

- Always check authentication status before API calls
- Handle 401/403 responses appropriately
- Use appropriate guard for admin vs user endpoints

### Security Considerations

- Never expose JWT secrets
- Validate all user inputs
- Implement rate limiting for auth endpoints
- Monitor failed authentication attempts
